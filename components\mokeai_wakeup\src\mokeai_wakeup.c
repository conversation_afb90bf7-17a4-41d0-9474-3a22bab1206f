#include "mokeai_wakeup.h"
#include "es7210.h"

static const char *TAG = "recorder";


#define audio_pipe_safe_free(x, fn) do { \
    if (x) {                             \
        fn(x);                           \
        x = NULL;                        \
    }                                    \
} while (0)
#define AUDIO_ADC_INPUT_CH_FORMAT "RMNM"

static audio_element_handle_t create_record_i2s_stream(void);
static audio_element_handle_t create_record_raw_stream(void);
static esp_err_t recorder_pipeline_run(recorder_pipeline_handle_t pipeline);
static esp_err_t recorder_pipeline_close(recorder_pipeline_handle_t pipeline);
static esp_err_t recorder_pipeline_stop(recorder_pipeline_handle_t pipeline);
static recorder_sr_cfg_t get_default_audio_record_config(void);


static struct wakeup_recorder s_wakeup_recorder = {0};

static esp_err_t recorder_pipeline_open(struct wakeup_recorder *self)
{
    esp_err_t ret = ESP_OK;
    ESP_LOGI(TAG, "%s", __func__);

    self->pipeline->record_state = PIPE_STATE_IDLE1;
    
    ESP_LOGI(TAG, "Create audio pipeline for recording");
    audio_pipeline_cfg_t pipeline_cfg = DEFAULT_AUDIO_PIPELINE_CONFIG();
    self->pipeline->audio_pipeline = audio_pipeline_init(&pipeline_cfg);
    mem_assert(self->pipeline->audio_pipeline);

    ESP_LOGI(TAG, "Create i2s stream handle for recording");
    self->pipeline->i2s_stream_reader = create_record_i2s_stream();

    ESP_LOGI(TAG, "Create raw stream handle for recording");
    self->pipeline->raw_reader = create_record_raw_stream();

    ESP_LOGI(TAG, " Register all player elements to audio pipeline");
    audio_pipeline_register(self->pipeline->audio_pipeline, self->pipeline->i2s_stream_reader, "record_i2s");
    audio_pipeline_register(self->pipeline->audio_pipeline, self->pipeline->raw_reader, "record_raw");
    ESP_LOGI(TAG, " Link all player elements to audio pipeline");
    const char *link_tag[2] = {"record_i2s", "record_raw"};
    audio_pipeline_link(self->pipeline->audio_pipeline, &link_tag[0], 2);

    return ret;
}

static esp_err_t recorder_pipeline_run(recorder_pipeline_handle_t pipeline)
{
    ESP_RETURN_ON_FALSE(pipeline != NULL, ESP_FAIL, TAG, "recorder pipeline not initialized");

    audio_pipeline_run(pipeline->audio_pipeline);
    pipeline->record_state = PIPE_STATE_RUNNING1;
    return ESP_OK;
}


static esp_err_t recorder_pipeline_stop(recorder_pipeline_handle_t pipeline)
{
    ESP_RETURN_ON_FALSE(pipeline != NULL, ESP_FAIL, TAG, "recorder pipeline not initialized");
    audio_pipeline_stop(pipeline->audio_pipeline);
    audio_pipeline_wait_for_stop(pipeline->audio_pipeline);
    pipeline->record_state = PIPE_STATE_IDLE1;
    return ESP_OK;
};


static esp_err_t recorder_pipeline_close(recorder_pipeline_handle_t pipeline)
{
    ESP_RETURN_ON_FALSE(pipeline != NULL, ESP_FAIL, TAG, "recorder pipeline not initialized");
    audio_pipeline_terminate(pipeline->audio_pipeline);
    audio_pipeline_unregister(pipeline->audio_pipeline, pipeline->i2s_stream_reader);
    audio_pipeline_unregister(pipeline->audio_pipeline, pipeline->raw_reader);

    /* Release all resources */
    audio_pipe_safe_free(pipeline->audio_pipeline, audio_pipeline_deinit);
    audio_pipe_safe_free(pipeline->raw_reader, audio_element_deinit);
    audio_pipe_safe_free(pipeline->i2s_stream_reader, audio_element_deinit);
    audio_pipe_safe_free(pipeline, audio_free);
    return ESP_OK;
}


#if 0
static int recorder_pipeline_read(recorder_pipeline_handle_t pipeline,char *buffer, int buf_size)
{

    ESP_LOGI(TAG, "recorder_pipeline_read %p", pipeline);
    ESP_LOGI(TAG, "recorder_pipeline_read %p", pipeline->raw_reader);
    ESP_LOGI(TAG, "recorder_pipeline_read %p", buffer);
    ESP_LOGI(TAG, "recorder_pipeline_read %d", buf_size);

    return raw_stream_read(pipeline->raw_reader, buffer,buf_size);
}

static int input_cb_for_afe(int16_t *buffer, int buf_sz, void *user_ctx, TickType_t ticks)
{
    //struct wakeup_recorder *self = (struct wakeup_recorder *)&s_wakeup_recorder;

    ESP_LOGI(TAG, "input_cb_for_afe %p", self);
    //return recorder_pipeline_read(self->pipeline, (char *)buffer, buf_sz);
    return ;
}

#endif


#if defined (ENABLE_AEC_DEBUG)
static void aec_debug_data_write(char *data, int len)
{
    if (record_flag) {
        if (sfp == NULL) {
            sfp = fopen(AEC_DEBUDG_FILE_NAME, "wb+");
            if (sfp == NULL) {
                ESP_LOGI(TAG, "Cannot open file, reason: %s\n", strerror(errno));
                return;
            }
        }
        fwrite(data, 1, len, sfp);

        if ((esp_timer_get_time() - start_tm) / 1000000 > AEC_RECORD_TIME) {
            record_flag = false;
            fclose(sfp);
            sfp = NULL;
            ESP_LOGI(TAG, "AEC debug data write done");
        }
    }
}
#endif // ENABLE_AEC_DEBUG

static int input_cb_for_afe(int16_t *buffer, int buf_sz, void *user_ctx, TickType_t ticks)
{
    if (s_wakeup_recorder.pipeline && s_wakeup_recorder.pipeline->raw_reader)
    {
        #if defined (ENABLE_AEC_DEBUG)
        aec_debug_data_write((char *)buffer, buf_sz);
        #endif // ENABLE_AEC_DEBUG
        
        return raw_stream_read(s_wakeup_recorder.pipeline->raw_reader, (char *)buffer, buf_sz);
    }
    else
        return 0;
}
static audio_element_handle_t create_record_i2s_stream(void)
{
    audio_element_handle_t i2s_stream = NULL;
    i2s_stream_cfg_t i2s_cfg = I2S_STREAM_CFG_DEFAULT_WITH_PARA(0, 16000, 32, AUDIO_STREAM_READER); 
    i2s_cfg.task_prio = 3;
    i2s_stream = i2s_stream_init(&i2s_cfg);
    return i2s_stream;
}



static audio_element_handle_t create_record_raw_stream(void)
{
    audio_element_handle_t raw_stream = NULL;
    raw_stream_cfg_t raw_cfg = RAW_STREAM_CFG_DEFAULT();
    raw_cfg.type = AUDIO_STREAM_WRITER;
    raw_cfg.out_rb_size = 2 * 1024;
    raw_stream = raw_stream_init(&raw_cfg);
    ESP_LOGD(TAG, "raw_stream %p", raw_stream);
    audio_element_set_output_timeout(raw_stream, portMAX_DELAY);
    return raw_stream;
}

static recorder_sr_cfg_t get_default_audio_record_config(void)
{
    #if 0 
    // ADF v2.7
    recorder_sr_cfg_t recorder_sr_cfg = DEFAULT_RECORDER_SR_CFG();
    
    recorder_sr_cfg.afe_cfg->memory_alloc_mode = AFE_MEMORY_ALLOC_MORE_PSRAM;
    recorder_sr_cfg.afe_cfg->vad_mode = VAD_MODE_4;
    recorder_sr_cfg.afe_cfg->wakenet_init = true;
    recorder_sr_cfg.afe_cfg->vad_init = true;
    recorder_sr_cfg.multinet_init = false;
    recorder_sr_cfg.afe_cfg->aec_init = true;
    recorder_sr_cfg.afe_cfg->memory_alloc_mode = AFE_MEMORY_ALLOC_MORE_PSRAM;
    // recorder_sr_cfg.afe_cfg.agc_mode = AFE_MN_PEAK_NO_AGC;
    return recorder_sr_cfg;
    #else
    // ADF master
    recorder_sr_cfg_t recorder_sr_cfg = DEFAULT_RECORDER_SR_CFG(AUDIO_ADC_INPUT_CH_FORMAT, "model", AFE_TYPE_SR, AFE_MODE_LOW_COST);
    recorder_sr_cfg.multinet_init = false;
    recorder_sr_cfg.fetch_task_core = 0;
    recorder_sr_cfg.feed_task_core = 1;
    recorder_sr_cfg.afe_cfg->vad_mode = VAD_MODE_4;
    recorder_sr_cfg.afe_cfg->memory_alloc_mode = AFE_MEMORY_ALLOC_MORE_PSRAM;
    recorder_sr_cfg.afe_cfg->agc_mode = AFE_MN_PEAK_NO_AGC;
    return recorder_sr_cfg;
    #endif
}

static inline int get_data_from_recorder(char *buffer, int buf_sz, TickType_t ticks)
{
    return audio_recorder_data_read(s_wakeup_recorder.recorder_engine, (char *)buffer, buf_sz, ticks);
}
struct wakeup_recorder * wakeup_audio_recorder_init(rec_event_cb_t cb, int vad_off_ms, int wakeup_end_ms)
{
    if (s_wakeup_recorder.initialized) return NULL;
    
    recorder_sr_cfg_t recorder_sr_cfg = get_default_audio_record_config();
    
    audio_rec_cfg_t cfg = AUDIO_RECORDER_DEFAULT_CFG();
    cfg.read = (recorder_data_read_t)input_cb_for_afe;
    ESP_LOGI(TAG, "s_wakeup_recorder %p", &s_wakeup_recorder);

    s_wakeup_recorder.pipeline        = audio_calloc(1, sizeof(struct recorder_pipeline));
    recorder_pipeline_open(&s_wakeup_recorder);
    cfg.sr_handle = recorder_sr_create(&recorder_sr_cfg, &cfg.sr_iface);
    cfg.event_cb = cb;
    cfg.vad_off = vad_off_ms;
    cfg.wakeup_time = 30000; // 唤醒无VAD事件触发到唤醒结束的时间 30s
    cfg.wakeup_end = wakeup_end_ms;

    s_wakeup_recorder.recorder_engine = audio_recorder_create(&cfg);

    s_wakeup_recorder.pipeline->run   = recorder_pipeline_run;
    s_wakeup_recorder.pipeline->close = recorder_pipeline_close;
    s_wakeup_recorder.pipeline->stop  = recorder_pipeline_stop;
    s_wakeup_recorder.read            = get_data_from_recorder;

    s_wakeup_recorder.initialized = true;
    
    es7210_adc_set_gain(ES7210_INPUT_MIC1 | ES7210_INPUT_MIC2, 9);
    es7210_adc_set_gain(ES7210_INPUT_MIC3, 12);
    es7210_adc_print_gains();

    return &s_wakeup_recorder;
}
