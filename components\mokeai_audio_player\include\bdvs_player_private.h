/**
 * Copyright (2017) Baidu Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * File: bdvs_player_private.h
 * Auth: Lisong <PERSON> (<EMAIL>)
 * Desc: bdvs player private header file
 */

#ifndef BDVS_PLAYER_PRIVATE_H
#define BDVS_PLAYER_PRIVATE_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#ifdef __cplusplus
extern "C" {
#endif

#define ONLY_FILENAME(x) (strrchr(x, '/') ? strrchr(x, '/')+1 : x)

#define DUER_LOGI(fmt, ...) 
#ifndef DUER_LOGI  
#define DUER_LOGI(fmt, ...)  printf("[I][%s:%d] " fmt "\n", ONLY_FILENAME(__FILE__), __LINE__, ##__VA_ARGS__)
#endif

#ifndef DUER_LOGE
#define DUER_LOGE(fmt, ...)  printf("[E][%s:%d] " fmt "\n", ONLY_FILENAME(__FILE__), __LINE__, ##__VA_ARGS__)
#endif

#ifndef DUER_LOGW
#define DUER_LOGW(fmt, ...)  printf("[W][%s:%d] " fmt "\n", ONLY_FILENAME(__FILE__), __LINE__, ##__VA_ARGS__)
#endif

#ifndef DUER_LOGD
#define DUER_LOGD(fmt, ...)
#endif

#ifndef MAX
#define MAX(a,b) 	((a)>(b)?(a):(b))
#endif

#ifndef MIN
#define MIN(a,b) 	((a)<(b)?(a):(b))
#endif

#ifdef __cplusplus
}
#endif

#endif

