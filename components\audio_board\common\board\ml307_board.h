#ifndef ML307_BOARD_H
#define ML307_BOARD_H

#include "base_board.h"
#include <ml307_at_modem.h>

class Ml307Board : public Board {
protected:
    Ml307AtModem modem_;

    virtual std::string GetBoardJson() override;
    void WaitForNetworkReady();

public:
    Ml307Board(gpio_num_t tx_pin, gpio_num_t rx_pin,size_t rx_buffer_size = 4096);
    std::string GetIpAddress() override;
    Ml307Board() = delete;
    virtual std::string GetBoardType() override;
    virtual void StartNetwork() override;
    virtual std::string GetBoardName() override {return "";};
    virtual Http* CreateHttp() override;
    virtual WebSocket* CreateWebSocket() override;
    virtual Mqtt* CreateMqtt() override;
    virtual Udp* CreateUdp() override;
    virtual void SetNetWorkKeepAlive(int keep_alive_seconds) override;
    virtual const char* GetNetworkStateIcon() override;
    virtual void SetPowerSaveMode(bool enabled) override;
    virtual void Setvolume(int vol) override {};
    virtual void Getvolume(int *vol) override {};
    virtual void SetDisEmoji(void *lv_obj, const char* type) override {};
    virtual void SetDisText(void *lv_obj,const char *text) override {};
    bool GetLocation(double& longitude, double& latitude);
};

#endif // ML307_BOARD_H
