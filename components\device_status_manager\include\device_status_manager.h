#ifndef DEVICE_STATUS_MANAGER_H
#define DEVICE_STATUS_MANAGER_H

#include <stdint.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"

typedef enum {
    TATE_BOOTING,           // 启动中
    STATE_DISCONNECTED,    // 未联网
    STATE_NETWORK_CONFIG,  // 配网中
    STATE_NETWORK_SUCCESS, // 配网成功
    STATE_NETWORK_SCANNING, // 搜索热点中
    STATE_NETWORK_FAILED,  // 配网失败
    STATE_NETWORK_CONNECTING_SERVER_FAILED,// 连接服务器失败
    STATE_AUTHENTICATING,  // 认证中
    STATE_AUTH_SUCCESS,    // 认证成功
    STATE_AUTH_FAILED,     // 认证失败
    STATE_WAKEUP,          // 唤醒中
    STATE_SLEEPING,        // 休眠中
    STATE_LISTENING,       // 聆听中
    STATE_THINKING,        // 思考中
    STATE_COMMAND_DONE,    // 指令完成
    STATE_RESULT_READY,    // 获得结果
    STATE_BUTTON_LONG_PRESS,    // 长按
    STATE_BUTTON_CLICK,         // 单击
    STATE_BUTTON_DOUBLE_CLICK,  // 双击
    STATE_BATT_CHARGING,       // 充电中
    STATE_BATT_DSCHARGING,      // 放电中
    STATE_BATT_LOW,             // 低电量
    STATE_BATT_CHARGING_DONE,   // 充电完成
    STATE_BATT_LEVEL_REPOART,   // 电量报告
    STATE_ABORTED,           // 异常退出
    STATE_ABORTED_WAIT_AUDIO_END, // 退出异常等待音频结束
    STATE_SERVER_RSP_SUCCECCED, // 服务器返回成功
    STATE_CHECKING_VERSION,  // 检查版本
    STATE_CHECKING_VERSION_FAILED,// 检查版本失败
    STATE_UPGRADING,            // 升级中
    STATE_UPGREADE_SUCCESS, // 升级成功,
    STATE_UPGARDE_PROCESS_REPORT,// 升级进度报告
    STATE_SYNC_TIME,            // 同步时间
    STATE_RECONNECTING,         // 重连中
    STATE_SERVER_RSP_ASR_TXT,
    STATE_SERVER_RSP_TTS_TXT,
    STATE_OPEN_STREAM_AUDIO_CH,
} DeviceState;


typedef struct {
    DeviceState state;
    void *state_data;
}DeviceStateMsg_t;

class DeviceStatusManger {
    public:
        static DeviceStatusManger& GetInstance() {
            static DeviceStatusManger instance;
            return instance;
        }
        DeviceStatusManger(const DeviceStatusManger&) = delete;
        DeviceStatusManger& operator=(const DeviceStatusManger&) = delete;

        void SetDeviceStatus(DeviceStateMsg_t *msg);
        void SetDeviceStatusFromInterurt(DeviceStateMsg_t *msg);
        xQueueHandle GetDeviceStatusQueue(void) const {return device_status_queue_;};
        DeviceState GetDeviceStatus() const {return device_status_;};
    private:
        DeviceState device_status_ = STATE_SLEEPING;
        xQueueHandle device_status_queue_; 
        DeviceStatusManger(); 
        static constexpr  const char *TAG = "DeviceStatusManger";
        ~DeviceStatusManger() {};
};


#endif

