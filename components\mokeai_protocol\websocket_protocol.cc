#include "websocket_protocol.h"
#include "base_board.h"
#include "system_info.h"

#include <cstring>
#include <cJSON.h>
#include <esp_log.h>
#include <arpa/inet.h>
#include <device_status_manager.h>

#define TAG "WS"

#define DEBUG_LOCAL
WebsocketProtocol::WebsocketProtocol() 
{
    event_group_handle_ = xEventGroupCreate();
    auto &board = Board::GetInstance();
    ServerMsgCacheInit(&tts_cache_);
    url_ = CONFIG_MOKEAI_WS_URL + \
        SystemInfo::GetMacAddress() + \
        "?" + "fwVersion=" + CONFIG_APP_PROJECT_VER +  \
         "&" + "deviceModel="+ CONFIG_MOKEAI_DEVICE_MODEL + \
         "&" + "ipAddress=" + board.GetIpAddress();
    task_id_ = "";
    ESP_LOGI(TAG, "Websocket URL: %s", url_.c_str());

    channel_semaphore_ = xSemaphoreCreateBinary();
    xSemaphoreGive(channel_semaphore_);
    ping_intervalms_ = 60000;

    xTaskCreate([](void* arg) {
        auto self = static_cast<WebsocketProtocol*>(arg);
        self->PingTimerCallback();
        vTaskDelete(NULL);
    }, "ping Task", 4096, this, 2, &ping_task_handle);
}

WebsocketProtocol::~WebsocketProtocol() {
    if (websocket_ != nullptr) {
        delete websocket_;
    }
    vEventGroupDelete(event_group_handle_);
}



void WebsocketProtocol::ServerMsgCacheInit(ServerMsgCache_t* cache) {
    cache->current_index = 0;
    cache->count = 0;
}

void WebsocketProtocol::ServerMsgCacheAddItem(ServerMsgCache_t* cache, const char* text) {
    if (cache->count >= MAX_CACHE_SIZE) {
        free(cache->buffer[cache->current_index]);
    }
    uint32_t len = strlen(text) + 1;
    cache->buffer[cache->current_index] = (char*)heap_caps_malloc(len, MALLOC_CAP_SPIRAM);
    strcpy(cache->buffer[cache->current_index], text);
    cache->current_index = (cache->current_index + 1) % MAX_CACHE_SIZE;
    if (cache->count < MAX_CACHE_SIZE) {
        cache->count++;
    }
}

char* WebsocketProtocol::ServerMsgCacheFlush(ServerMsgCache_t* cache) {
    if (cache->count == 0) return nullptr;
    
    uint32_t total_len = 0;
    for (int i = 0; i < cache->count; i++) {
        int idx = (cache->current_index - cache->count + i + MAX_CACHE_SIZE) % MAX_CACHE_SIZE;
        total_len += strlen(cache->buffer[idx]) + 1;
    }
    
    char* result = (char*)heap_caps_malloc(total_len, MALLOC_CAP_SPIRAM);
    char* output = result;
    
    for (int i = 0; i < cache->count; i++) {
        int idx = (cache->current_index - cache->count + i + MAX_CACHE_SIZE) % MAX_CACHE_SIZE;
        strcpy(output, cache->buffer[idx]);
        output += strlen(cache->buffer[idx]);
    }
    *output = '\0'; // 确保字符串终止
    
    for (int i = 0; i < MAX_CACHE_SIZE; i++) {
        free(cache->buffer[i]);
        cache->buffer[i] = nullptr;
    }
    cache->count = 0;
    cache->current_index = 0;
    return result;
}



void WebsocketProtocol::Start() 
{ 
    // register websocket callbacks
    if (websocket_ != nullptr) {
        return;
    }
    connection_state_ = kConnectionStateDisconnected;
    authentication_state_ = kAuthenticationStateNotAuthenticated;
    websocket_ = Board::GetInstance().CreateWebSocket();
    websocket_->OnDisconnected([this]() 
    {
        ESP_LOGI(TAG, "Websocket disconnected");
        // 当websocket连接断开时发起重连
        if (authentication_state_ == kAuthenticationStateAuthenticated)
        {
            auto &dev_state_manager = DeviceStatusManger::GetInstance();
            DeviceStateMsg_t  msg = {STATE_RECONNECTING, nullptr};
            dev_state_manager.SetDeviceStatus(&msg);
        }
    
        authentication_state_ = kAuthenticationStateNotAuthenticated;
        connection_state_ = kConnectionStateDisconnected;
        if (on_audio_channel_closed_ != nullptr) {
            on_audio_channel_closed_();
        }
    });

    websocket_->OnConnected([this]() 
    {
        connection_state_ = kConnectionStateConnected;
        ESP_LOGI(TAG, "Websocket connected");
    });
    websocket_->OnError([this](int code) 
    {
        authentication_state_ = kAuthenticationStateNotAuthenticated;
        connection_state_ = kConnectionStateDisconnected;
        ESP_LOGE(TAG, "Websocket error: %d", code);
        error_occurred_ = true;
    });

    websocket_->OnData([this](const char* data, size_t len, bool binary) 
    {
        auto &dev_state_manager = DeviceStatusManger::GetInstance();
        auto & board = Board::GetInstance();
        xSemaphoreTake(channel_semaphore_, portMAX_DELAY);
        if (binary)
        {
            if (on_incoming_audio_) { // 隐式检查是否可调用
                try {
                    on_incoming_audio_(
                        std::vector<uint8_t>(reinterpret_cast<const uint8_t*>(data), 
                        reinterpret_cast<const uint8_t*>(data) + len)
                    );
                } catch (const std::exception& e) {
                    ESP_LOGE(TAG, "Audio callback failed: %s", e.what());
                }
            }
        } 
        else if (len == 0) 
        {
            ESP_LOGE(TAG, "Received empty data");
            return;
        } 
        else 
        {
            ESP_LOGD(TAG, "Received text data: %s", data);
            // Parse JSON data
            auto root = cJSON_Parse(data);
            auto type = cJSON_GetObjectItem(root, "type");
            auto code = cJSON_GetObjectItem(root, "code");
            auto result = cJSON_GetObjectItem(root, "result");
            auto timestamp = cJSON_GetObjectItem(root, "timestamp");
            auto taskid    = cJSON_GetObjectItem(root, "taskId");
            if (type && type->valuestring)
                msg_type_ = type->valuestring;

            ESP_LOGI(TAG, "Server response type [%s] content [%s]", type ? type->valuestring: "No type", result ? result->valuestring : "No result");
            // Check for HTTP Status Code
            if (code && cJSON_IsNumber(code)) 
            {
                server_ret_code_ = code->valueint;

                if(code->valueint != 200) {
                    ESP_LOGI(TAG, "HTTP Status Code: %d", code->valueint);
                }
                if (code->valueint == PROT_ERROR_CODE)
                {
                    if (strcmp(prev_task_id_.c_str(), taskid->valuestring) != 0)
                    {
                        prev_task_id_ = taskid->valuestring;
                        ESP_LOGI(TAG, "open stream audio ch to revice audio task id [%s]", taskid->valuestring);
                        static uint32_t open_vad_time_ms = 2000;
                        DeviceStateMsg_t msg = {STATE_OPEN_STREAM_AUDIO_CH, &open_vad_time_ms};
                        dev_state_manager.SetDeviceStatus(&msg);
                    }
                    DeviceStateMsg_t msg = {STATE_ABORTED, nullptr};
                    dev_state_manager.SetDeviceStatus(&msg);
                }
                else if (code->valueint == PROT_TIMEOUT_CODE)
                {
                    if (strcmp(prev_task_id_.c_str(), taskid->valuestring) != 0)
                    {
                        prev_task_id_ = taskid->valuestring;
                        ESP_LOGI(TAG, "open stream audio ch to revice audio task id [%s]", taskid->valuestring);
                        DeviceStateMsg_t msg = {STATE_OPEN_STREAM_AUDIO_CH, nullptr};
                        dev_state_manager.SetDeviceStatus(&msg);
                    }
                    DeviceStateMsg_t msg = {STATE_ABORTED, nullptr};
                    dev_state_manager.SetDeviceStatus(&msg);
                }
                else if (code->valueint == PROT_SUCCESSED_CODE)
                {

                }
                else if (code->valueint == PROT_DEV_CERT_SUCCESS)
                {
                    ESP_LOGI(TAG, "device auth success");
                    authentication_state_ = kAuthenticationStateAuthenticated;
                    static bool is_sync = false;

                    if (is_sync == false)
                    {
                        static uint64_t timestamp_ = timestamp->valueint;
                        is_sync = true;
                        DeviceStateMsg_t msg = {STATE_SYNC_TIME, &timestamp_};
                        dev_state_manager.SetDeviceStatus(&msg);
                    }
                    xEventGroupSetBits(event_group_handle_, WEBSOCKET_PROTOCOL_SERVER_AUTH_EVENT);
                    goto end;
                }
                else if (code->valueint == PROT_DEV_STOP_CODE)
                {
                    DeviceStateMsg_t msg = {STATE_ABORTED_WAIT_AUDIO_END, nullptr};
                    dev_state_manager.SetDeviceStatus(&msg);
                }
                else if (code->valueint == PROT_DEV_AUTHING_CODE)
                {
                    ESP_LOGE(TAG, "Device is authenticating, please wait...[%s]\n", result ? result->valuestring : "No result");
                }
                else
                {
                    ESP_LOGE(TAG, "Unknown error code: %d", code->valueint);
                }
            }
            
            // Check for message type
            if (type && cJSON_IsString(type)) 
            {
                if (strcmp(type->valuestring, "asr") == 0)
                {
                    if (result && cJSON_IsString(result))
                    {
                        if (board.GetBoardName() == "mokeai_desk")
                        {
                            DeviceStateMsg_t msg = {STATE_SERVER_RSP_ASR_TXT, nullptr};
                            static char *asr_text = nullptr;
                            uint32_t len = strlen((const char *)result->valuestring) + 1;
                            ESP_LOGI(TAG, "Received ASR len: %ld", len);
                            asr_text = (char *)heap_caps_malloc(len, MALLOC_CAP_SPIRAM);
                            memset(asr_text, 0, len);
                            strcpy(asr_text, result->valuestring);
                            msg.state_data = asr_text;
                            dev_state_manager.SetDeviceStatus(&msg);
                        }
                        ESP_LOGD(TAG, "Received ASR result: %s", result->valuestring);
                    }
                }
                else if (strcmp(type->valuestring, "tts") == 0)
                {
                    if (result && cJSON_IsString(result))
                    {
                        if (board.GetBoardName() == "mokeai_desk")
                        {
                            if (tts_cache_.count < MAX_CACHE_SIZE)
                                ServerMsgCacheAddItem(&tts_cache_, result->valuestring);
                            else
                            {
                                DeviceStateMsg_t msg = {STATE_SERVER_RSP_TTS_TXT, nullptr};
                                msg.state_data = ServerMsgCacheFlush(&tts_cache_);
                                msg.state = STATE_SERVER_RSP_TTS_TXT;
                                dev_state_manager.SetDeviceStatus(&msg);
                                ESP_LOGI(TAG, "Sent msg : %s", (char *)msg.state_data);
                                
                                ServerMsgCacheAddItem(&tts_cache_, result->valuestring);
                            }
                            ESP_LOGI(TAG, "Received TTS result: %s", result->valuestring);
                        }
                        // check for task id
                        if (strcmp(prev_task_id_.c_str(), taskid->valuestring) != 0)
                        {
                            static uint32_t open_vad_time_ms = 2000;
                            prev_task_id_ = taskid->valuestring;
                            ESP_LOGI(TAG, "open stream audio ch to revice audio task id [%s]", taskid->valuestring);
                            DeviceStateMsg_t msg = {STATE_OPEN_STREAM_AUDIO_CH, &open_vad_time_ms};
                            dev_state_manager.SetDeviceStatus(&msg);
                        }
                    }

                }
                else if (strcmp(type->valuestring, "error") == 0)
                {
                    
                }
                else if (strcmp(type->valuestring, "handshake") == 0)
                {
                    ESP_LOGI(TAG, "server time : %d", timestamp->valueint);
                }
                else if (strcmp(type->valuestring, "auth") == 0) 
                {
                    
                }
                else if (strcmp(type->valuestring, "setvolume") == 0)
                {
                    auto &board = Board::GetInstance();

                    if (result && cJSON_IsString(result))
                    {
                        ESP_LOGI(TAG, "Received setvolume result: %s", result->valuestring);
                    }
                    board.Setvolume(atoi(result->valuestring));
                }
                else if (strcmp(type->valuestring, "getvolume") == 0)
                {
                }
                else
                {

                }
    
            }
            else 
            {
                ESP_LOGE(TAG, "Invalid JSON data: %s", data);
            }
end:
            cJSON_Delete(root);
        }
        xSemaphoreGive(channel_semaphore_);} );
}

void WebsocketProtocol::SendAudio(const std::vector<uint8_t>& data) {
    if (websocket_ == nullptr) {
        return;
    }

    xSemaphoreTake(channel_semaphore_, portMAX_DELAY);
    websocket_->Send(data.data(), data.size(), true);
    xSemaphoreGive(channel_semaphore_);
}

void WebsocketProtocol::SendText(const std::string& text) {
    if (websocket_ == nullptr) {
        return;
    }
    
    xSemaphoreTake(channel_semaphore_, portMAX_DELAY);
    if (!websocket_->Send(text)) {
        ESP_LOGE(TAG, "Failed to send text: %s", text.c_str());
        //SetError(Lang::Strings::SERVER_ERROR);
    }
    
    xSemaphoreGive(channel_semaphore_);
}

bool WebsocketProtocol::IsAudioChannelOpened() const {
    return websocket_ != nullptr && websocket_->IsConnected() && !error_occurred_ && !IsTimeout();
}

void WebsocketProtocol::CloseAudioChannel() 
{
    if (websocket_ == nullptr) {
        return ;
    }

    delete websocket_;
    websocket_ = nullptr;
    ESP_LOGI(TAG, "Websocket deleted");
}

void WebsocketProtocol::SendStartListening(ListeningMode mode) 
{
   
    auto now = std::chrono::system_clock::now();
    auto epoch = now.time_since_epoch();
    auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(epoch).count();
    int current_volume = 0;
    auto &board = Board::GetInstance();
    // 创建cJSON根对象

    // 获取当前音量
    board.Getvolume(&current_volume);
    std::string current_volume_str = std::to_string(current_volume);
    
    cJSON *root = cJSON_CreateObject();
    
    // 添加各字段
    cJSON_AddStringToObject(root, "type", "startListening");
    cJSON_AddStringToObject(root, "macId", SystemInfo::GetMacAddress().c_str());
    cJSON_AddStringToObject(root, "fwVersion", CONFIG_APP_PROJECT_VER);
    cJSON_AddStringToObject(root, "deviceModel", CONFIG_MOKEAI_DEVICE_MODEL);
    cJSON_AddBoolToObject(root, "end", false);
    
    task_id_ = SystemInfo::GetMacAddress() + "-" + std::to_string(milliseconds);
    cJSON_AddStringToObject(root, "taskId", task_id_.c_str());

    cJSON_AddStringToObject(root, "volume", current_volume_str.c_str());

   
    cJSON_AddBoolToObject(root, "interrupt", true);

    cJSON_AddStringToObject(root, "topicId", topic_id_.c_str());
    cJSON_AddStringToObject(root, "ipAddress", board.GetIpAddress().c_str());

    // 生成JSON字符串
    char *json_str = cJSON_PrintUnformatted(root);
    ESP_LOGI(TAG, "Send start listening: %s", json_str);
    xSemaphoreTake(channel_semaphore_, portMAX_DELAY);
    if (!websocket_->Send(json_str)) {
        ESP_LOGE(TAG, "Failed to send text: %s", json_str);
        //SetError(Lang::Strings::SERVER_ERROR);
    }
    xSemaphoreGive(channel_semaphore_);
    // 释放资源
    free(json_str);
    cJSON_Delete(root);
}
void WebsocketProtocol::PingTimerCallback(void)
{
    while (true)
    {
        if(xSemaphoreTake(channel_semaphore_, portMAX_DELAY))
        {
            if (websocket_ != nullptr && websocket_->IsConnected())
            {
                ESP_LOGD(TAG, "Device connetion is idle Websocket send ping to server");
                websocket_->Ping();
            }
            xSemaphoreGive(channel_semaphore_);
        }
        vTaskDelay(ping_intervalms_ / portTICK_PERIOD_MS);
    }
}

enum connection_state WebsocketProtocol::GetConnectionState() const
{
    return connection_state_;
}


enum authentication_state WebsocketProtocol::GetAuthenticationState() const
{
    return authentication_state_;
}
void WebsocketProtocol::SendWakeWordDetected(const std::string& wake_word) 
{
    auto now = std::chrono::system_clock::now();
    auto epoch = now.time_since_epoch();
    auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(epoch).count();
    int current_volume = 0;
    auto &board = Board::GetInstance();
    // 创建cJSON根对象

    // 获取当前音量
    board.Getvolume(&current_volume);
    std::string current_volume_str = std::to_string(current_volume);
    
    cJSON *root = cJSON_CreateObject();
    
    topic_id_ = board.GetUuid() + "-" + std::to_string(milliseconds);
    // 添加各字段
    cJSON_AddStringToObject(root, "type", "wakeup");
    cJSON_AddStringToObject(root, "macId", SystemInfo::GetMacAddress().c_str());
    cJSON_AddStringToObject(root, "fwVersion", CONFIG_APP_PROJECT_VER);
    cJSON_AddStringToObject(root, "deviceModel", CONFIG_MOKEAI_DEVICE_MODEL);
    cJSON_AddBoolToObject(root, "end", false);
    
    task_id_ = SystemInfo::GetMacAddress() + "-" + std::to_string(milliseconds);
    
    cJSON_AddStringToObject(root, "taskId", task_id_.c_str());

    cJSON_AddStringToObject(root, "volume", current_volume_str.c_str());

    cJSON_AddBoolToObject(root, "interrupt", true);
    
    cJSON_AddStringToObject(root, "topicId", topic_id_.c_str());
    cJSON_AddStringToObject(root, "ipAddress", board.GetIpAddress().c_str());

    // 生成JSON字符串
    char *json_str = cJSON_PrintUnformatted(root);

    xSemaphoreTake(channel_semaphore_, portMAX_DELAY);
    if (!websocket_->Send(json_str)) {
        ESP_LOGE(TAG, "Failed to send text: %s", json_str);
        //SetError(Lang::Strings::SERVER_ERROR);
    }
    
    ESP_LOGI(TAG, "Send wake word detected: %s", json_str);
    xSemaphoreGive(channel_semaphore_);
    // 释放资源
    free(json_str);
    cJSON_Delete(root);
}


void WebsocketProtocol::SendTouchDetect(void)
{
    int current_volume = 0;
    auto & board = Board::GetInstance();
    auto now = std::chrono::system_clock::now();
    auto epoch = now.time_since_epoch();
    auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(epoch).count();
    std::string task_id = SystemInfo::GetMacAddress() + "-" + std::to_string(milliseconds);
    std::string topic_id = board.GetUuid() + "-" + std::to_string(milliseconds);

    cJSON *root = cJSON_CreateObject();
    
    // 获取当前音量
    board.Getvolume(&current_volume);
    std::string current_volume_str = std::to_string(current_volume);
    // 添加各字段
    cJSON_AddBoolToObject(root, "end", false);
    cJSON_AddStringToObject(root, "type", "touchDetect");

    
    cJSON_AddStringToObject(root, "taskId", task_id.c_str());

    cJSON_AddBoolToObject(root, "interrupt", true);
    cJSON_AddStringToObject(root, "topicId", topic_id.c_str());
    
    cJSON_AddStringToObject(root, "volume", current_volume_str.c_str());

    // 生成JSON字符串
    char *json_str = cJSON_PrintUnformatted(root);
    ESP_LOGI(TAG, "Send touch detect msg: %s", json_str);

    xSemaphoreTake(channel_semaphore_, portMAX_DELAY);
    if (!websocket_->Send(json_str)) {
        ESP_LOGE(TAG, "Failed to send text: %s", json_str);
        //SetError(Lang::Strings::SERVER_ERROR);
    }
    xSemaphoreGive(channel_semaphore_);
    // 释放资源
    free(json_str);
    cJSON_Delete(root);
}


void WebsocketProtocol::SendStopListening() 
{
    int current_volume = 0;
    auto & board = Board::GetInstance();

    cJSON *root = cJSON_CreateObject();
    
    // 获取当前音量
    board.Getvolume(&current_volume);
    std::string current_volume_str = std::to_string(current_volume);
    // 添加各字段
    cJSON_AddBoolToObject(root, "end", true);
    cJSON_AddStringToObject(root, "type", "stopListening");

    
    cJSON_AddStringToObject(root, "taskId", task_id_.c_str());

    cJSON_AddBoolToObject(root, "interrupt", true);
    cJSON_AddStringToObject(root, "topicId", topic_id_.c_str());
    
    cJSON_AddStringToObject(root, "volume", current_volume_str.c_str());

    // 生成JSON字符串
    char *json_str = cJSON_PrintUnformatted(root);
    ESP_LOGI(TAG, "Send stop listening: %s", json_str);

    xSemaphoreTake(channel_semaphore_, portMAX_DELAY);
    if (!websocket_->Send(json_str)) {
        ESP_LOGE(TAG, "Failed to send text: %s", json_str);
        //SetError(Lang::Strings::SERVER_ERROR);
    }
    xSemaphoreGive(channel_semaphore_);
    // 释放资源
    free(json_str);
    cJSON_Delete(root);
}

void WebsocketProtocol::SendManualExit()
{
    int current_volume = 0;
    auto & board = Board::GetInstance();

    cJSON *root = cJSON_CreateObject();
    
    // 获取当前音量
    board.Getvolume(&current_volume);
    std::string current_volume_str = std::to_string(current_volume);
    // 添加各字段
    cJSON_AddBoolToObject(root, "end", false);
    cJSON_AddStringToObject(root, "type", "Manualexit");

    
    cJSON_AddStringToObject(root, "taskId","");

    cJSON_AddBoolToObject(root, "interrupt", true);
    cJSON_AddStringToObject(root, "topicId", "");
    
    cJSON_AddStringToObject(root, "volume", current_volume_str.c_str());

    // 生成JSON字符串
    char *json_str = cJSON_PrintUnformatted(root);
    ESP_LOGI(TAG, "Send Manual exit: %s", json_str);

    xSemaphoreTake(channel_semaphore_, portMAX_DELAY);
    if (!websocket_->Send(json_str)) {
        ESP_LOGE(TAG, "Failed to send text: %s", json_str);
        //SetError(Lang::Strings::SERVER_ERROR);
    }
    xSemaphoreGive(channel_semaphore_);
    // 释放资源
    free(json_str);
    cJSON_Delete(root);
}
void WebsocketProtocol::SendAutoExit() 
{
    int current_volume = 0;
    auto & board = Board::GetInstance();

    cJSON *root = cJSON_CreateObject();
    
    // 获取当前音量
    board.Getvolume(&current_volume);
    std::string current_volume_str = std::to_string(current_volume);
    // 添加各字段
    cJSON_AddBoolToObject(root, "end", false);
    cJSON_AddStringToObject(root, "type", "Autoexit");

    
    cJSON_AddStringToObject(root, "taskId", "");

    cJSON_AddBoolToObject(root, "interrupt", true);
    cJSON_AddStringToObject(root, "topicId", "");
    
    cJSON_AddStringToObject(root, "volume", current_volume_str.c_str());

    // 生成JSON字符串
    char *json_str = cJSON_PrintUnformatted(root);
    ESP_LOGI(TAG, "Send auto exit: %s", json_str);

    xSemaphoreTake(channel_semaphore_, portMAX_DELAY);
    if (!websocket_->Send(json_str)) {
        ESP_LOGE(TAG, "Failed to send text: %s", json_str);
        //SetError(Lang::Strings::SERVER_ERROR);
    }
    xSemaphoreGive(channel_semaphore_);
    // 释放资源
    free(json_str);
    cJSON_Delete(root);
}


bool WebsocketProtocol::DeviceAuthenticate(void)
{
    auto &dev_state_manager = DeviceStatusManger::GetInstance();
    
    if (websocket_ == nullptr) {
        return false;
    }

    if (websocket_->IsConnected()) {
        ESP_LOGI(TAG, "Websocket already connected");
    }
    else
    {
        ESP_LOGI(TAG, "Websocket connecting ....");
        if (!websocket_->Connect(url_.c_str())) 
        {
            ESP_LOGE(TAG, "Failed to connect to websocket server");
            DeviceStateMsg_t  msg = {STATE_NETWORK_CONNECTING_SERVER_FAILED, nullptr};
            dev_state_manager.SetDeviceStatus(&msg);
            return false;
        }
    }
    // wait for server auth info
    DeviceStateMsg_t  msg = {STATE_AUTHENTICATING, nullptr};
    dev_state_manager.SetDeviceStatus(&msg);
    EventBits_t auth_bit = xEventGroupWaitBits(event_group_handle_, WEBSOCKET_PROTOCOL_SERVER_AUTH_EVENT, pdTRUE, pdFALSE, portMAX_DELAY);
    if (!(auth_bit & WEBSOCKET_PROTOCOL_SERVER_AUTH_EVENT)) {
        ESP_LOGE(TAG, "Failed to receive server hello");
        DeviceStateMsg_t  msg = {STATE_AUTH_FAILED, nullptr};
        dev_state_manager.SetDeviceStatus(&msg);
        return false;
    }
    msg.state_data = NULL;
    msg.state = STATE_AUTH_SUCCESS;
    dev_state_manager.SetDeviceStatus(&msg);
    // auth successed Close the websocket
    if (on_audio_channel_opened_ != nullptr) {
        on_audio_channel_opened_();
    }
    // 开机连接后，和服务器不断开连接，提升速度
    return true;
}
bool WebsocketProtocol::OpenAudioChannel(void) 
{
    auto &dev_state_manager = DeviceStatusManger::GetInstance();

    if (websocket_->IsConnected()) {
        ESP_LOGI(TAG, "Websocket already connected");
        return true;
    }
    else
    {
        connection_state_ = kConnectionStateConnecting;
        ESP_LOGI(TAG, "Websocket connecting ....");
        if (!websocket_->Connect(url_.c_str())) 
        {
            connection_state_ = kConnectionStateDisconnected;
            ESP_LOGE(TAG, "Failed to connect to websocket server");
            DeviceStateMsg_t  msg = {STATE_NETWORK_CONNECTING_SERVER_FAILED, nullptr};
            dev_state_manager.SetDeviceStatus(&msg);
            return false;
        }
    }
    return true;
}
