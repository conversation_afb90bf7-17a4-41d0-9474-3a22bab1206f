#ifndef __TOUCH_BUTTON_H__
#define __TOUCH_BUTTON_H__

#include <cstdint>
#include <functional>
#include <freertos/FreeRTOS.h>
#include <driver/touch_pad.h>
#include "freertos/queue.h"

class TouchSensor 
{
public:
    
    explicit TouchSensor(uint8_t pad_num = TOUCH_PAD_NUM4, float threshold = 0.2f);
    ~TouchSensor();
    
    void OnTouch(std::function<void()>  cb) { on_touch_cb_ = cb; }
    std::function<void()> on_touch_cb_ = nullptr;
    QueueHandle_t que_touch = NULL;
private:
    static void interruptHandler(void* arg);
    static void touch_state_recive(void *pvParameter);
    uint8_t padnum_;
    float threshold_;
};

#endif
