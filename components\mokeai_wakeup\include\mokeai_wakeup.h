#ifndef BDVS_WAKEUP1_H
#define BDVS_WAKEUP1_H




#ifdef __cplusplus
extern "C" {
#endif

#include <string.h>
#include "esp_log.h"
#include "esp_timer.h"
#include "esp_check.h"
#include "sdkconfig.h"
#include "audio_recorder.h"
#include "recorder_sr.h"
#include "recorder_encoder.h"
#include "audio_element.h"
#include "filter_resample.h"
#include "audio_mem.h"
#include "audio_thread.h"
#include "audio_pipeline.h"
#include "audio_common.h"
#include "i2s_stream.h"
#include "raw_stream.h"

/**
 * @brief Enumeration of player pipeline states
 */
typedef enum {
    PIPE_STATE_IDLE1,     /**< The pipeline is idle and not processing any audio */
    PIPE_STATE_RUNNING1,  /**< The pipeline is actively processing and playing audio */
} pipe_player_state_e1;


struct recorder_pipeline{
    audio_pipeline_handle_t audio_pipeline;
    audio_element_handle_t  i2s_stream_reader;
    audio_element_handle_t  audio_encoder;
    audio_element_handle_t  raw_reader;
    audio_element_handle_t  resample;
    audio_rec_handle_t      recorder_engine;
    pipe_player_state_e1     record_state;
    esp_err_t (*open)(struct recorder_pipeline *pipeline);
    esp_err_t (*run)(struct recorder_pipeline *pipeline);
    esp_err_t (*stop)(struct recorder_pipeline *pipeline);
    esp_err_t (*close)(struct recorder_pipeline *pipeline);
};

typedef struct recorder_pipeline *recorder_pipeline_handle_t;


struct wakeup_recorder
{
    struct recorder_pipeline *pipeline; // 修改：将结构体改为指针类型
    audio_rec_handle_t recorder_engine; // 新增：添加缺失的recorder_engine字段
    int (*read)(char *buffer, int buf_size, TickType_t ticks);
    bool initialized; // 新增状态标记
};

struct wakeup_recorder * wakeup_audio_recorder_init(rec_event_cb_t cb, int vad_off_ms, int wakeup_end_ms);


void start_recorder(void);
#ifdef __cplusplus
}
#endif

#endif
