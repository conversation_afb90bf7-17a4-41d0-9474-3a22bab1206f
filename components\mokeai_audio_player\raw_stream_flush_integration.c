/*
 * Raw Stream 冲刷功能集成示例
 * 
 * 展示如何在实际的音频流处理中集成 raw stream 静音冲刷功能
 */

#include "audio_processor.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"

static const char *TAG = "RAW_STREAM_FLUSH";

// 事件标志
#define AUDIO_STREAM_CONNECTED    BIT0
#define AUDIO_STREAM_DISCONNECTED BIT1
#define AUDIO_STREAM_ERROR        BIT2

static EventGroupHandle_t audio_event_group = NULL;
static audio_player_t *player = NULL;
static bool stream_active = false;

/**
 * 音频流连接状态监控任务
 */
void audio_stream_monitor_task(void *pvParameters)
{
    EventBits_t bits;
    
    while (1) {
        // 等待音频流事件
        bits = xEventGroupWaitBits(audio_event_group,
                                   AUDIO_STREAM_CONNECTED | AUDIO_STREAM_DISCONNECTED | AUDIO_STREAM_ERROR,
                                   pdTRUE, pdFALSE, portMAX_DELAY);
        
        if (bits & AUDIO_STREAM_CONNECTED) {
            ESP_LOGI(TAG, "Audio stream connected");
            stream_active = true;
            
            // 启动音频播放管道
            if (player && player->stream_pipeline) {
                player->stream_pipeline->player_pipeline_run(player->stream_pipeline, NULL);
            }
        }
        
        if (bits & AUDIO_STREAM_DISCONNECTED) {
            ESP_LOGW(TAG, "Audio stream disconnected - flushing raw stream");
            stream_active = false;
            
            // 音频流断开时，立即冲刷 raw stream
            esp_err_t ret = audio_player_flush_pipeline_silence();
            if (ret == ESP_OK) {
                ESP_LOGI(TAG, "Raw stream flushed successfully after disconnection");
            } else {
                ESP_LOGE(TAG, "Failed to flush raw stream after disconnection");
            }
        }
        
        if (bits & AUDIO_STREAM_ERROR) {
            ESP_LOGE(TAG, "Audio stream error - flushing raw stream");
            stream_active = false;
            
            // 音频流错误时，也进行冲刷
            esp_err_t ret = audio_player_flush_pipeline_silence();
            if (ret == ESP_OK) {
                ESP_LOGI(TAG, "Raw stream flushed successfully after error");
            } else {
                ESP_LOGE(TAG, "Failed to flush raw stream after error");
            }
        }
    }
}

/**
 * 模拟音频数据写入函数
 */
esp_err_t write_audio_data(const uint8_t *data, size_t len)
{
    if (!stream_active || !player || !player->stream_pipeline) {
        ESP_LOGW(TAG, "Stream not active, discarding %d bytes", len);
        return ESP_FAIL;
    }
    
    if (player->stream_pipeline->player_pipeline_write) {
        size_t written = player->stream_pipeline->player_pipeline_write((void*)data, len);
        if (written != len) {
            ESP_LOGW(TAG, "Partial write: %d/%d bytes", written, len);
            return ESP_FAIL;
        }
        return ESP_OK;
    }
    
    return ESP_FAIL;
}

/**
 * 模拟网络连接状态变化
 */
void simulate_network_events(void)
{
    // 模拟连接
    ESP_LOGI(TAG, "Simulating network connection...");
    xEventGroupSetBits(audio_event_group, AUDIO_STREAM_CONNECTED);
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 模拟一些音频数据
    uint8_t dummy_audio[1024] = {0x12, 0x34, 0x56, 0x78};
    for (int i = 0; i < 5; i++) {
        write_audio_data(dummy_audio, sizeof(dummy_audio));
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    
    // 模拟断开连接
    ESP_LOGI(TAG, "Simulating network disconnection...");
    xEventGroupSetBits(audio_event_group, AUDIO_STREAM_DISCONNECTED);
    vTaskDelay(pdMS_TO_TICKS(500));
    
    // 模拟重新连接
    ESP_LOGI(TAG, "Simulating network reconnection...");
    xEventGroupSetBits(audio_event_group, AUDIO_STREAM_CONNECTED);
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 模拟错误
    ESP_LOGI(TAG, "Simulating network error...");
    xEventGroupSetBits(audio_event_group, AUDIO_STREAM_ERROR);
}

/**
 * 初始化 raw stream 冲刷集成
 */
esp_err_t init_raw_stream_flush_integration(void)
{
    // 创建事件组
    audio_event_group = xEventGroupCreate();
    if (!audio_event_group) {
        ESP_LOGE(TAG, "Failed to create event group");
        return ESP_FAIL;
    }
    
    // 创建音频播放器
    player = create_audio_player();
    if (!player) {
        ESP_LOGE(TAG, "Failed to create audio player");
        return ESP_FAIL;
    }
    
    // 创建监控任务
    xTaskCreate(audio_stream_monitor_task, "audio_monitor", 4096, NULL, 5, NULL);
    
    ESP_LOGI(TAG, "Raw stream flush integration initialized");
    return ESP_OK;
}

/**
 * 主函数示例
 */
void raw_stream_flush_demo(void)
{
    ESP_LOGI(TAG, "Starting raw stream flush demo");
    
    // 初始化
    if (init_raw_stream_flush_integration() != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize");
        return;
    }
    
    // 模拟网络事件
    simulate_network_events();
    
    ESP_LOGI(TAG, "Raw stream flush demo completed");
}

/*
 * 集成要点：
 * 
 * 1. 在音频流断开时立即调用 audio_player_flush_pipeline_silence()
 * 2. 在网络错误时也进行冲刷，防止错误数据残留
 * 3. 使用事件驱动的方式处理连接状态变化
 * 4. 确保在管道运行状态下才进行冲刷
 * 5. 记录冲刷结果，便于调试和监控
 */
