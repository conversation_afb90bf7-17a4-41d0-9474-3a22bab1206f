#include "motor/drv8834.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_timer.h"

static const char *TAG = "drv8834";
drv8834::drv8834(gpio_num_t dir_pin, 
                gpio_num_t step_pin,
                gpio_num_t enable_pin) 
    : dir_pin(dir_pin), step_pin(step_pin), enable_pin(enable_pin) {
    
    gpio_config_t io_conf;
    io_conf.mode = GPIO_MODE_OUTPUT;
    io_conf.pull_down_en = GPIO_PULLDOWN_DISABLE;
    io_conf.pull_up_en = GPIO_PULLUP_ENABLE;
    io_conf.intr_type = GPIO_INTR_DISABLE;    // 禁用中断

    io_conf.pin_bit_mask = (1ULL << dir_pin) | (1ULL << step_pin) | (1ULL << enable_pin);

    gpio_config(&io_conf);
    ESP_LOGI(TAG, "dir_pin = %d step_pin = %d enable_pin = %d", dir_pin, step_pin, enable_pin);
    disable();
}

void drv8834::set_direction(bool direction) {
    gpio_set_level(dir_pin, direction);
}

void drv8834::enable() {
    gpio_set_level(enable_pin, 0);
    
}

void drv8834::disable() {
    gpio_set_level(enable_pin, 1);
}



void drv8834::delay_us(uint32_t us) {
    uint64_t start = esp_timer_get_time();
    while(esp_timer_get_time() - start < us);
}


void drv8834::step(uint32_t steps) {
    for(uint32_t i = 0; i < steps; i++) {
        gpio_set_level(step_pin, 1);
        delay_us(speed_us_);
        gpio_set_level(step_pin, 0);
        delay_us(speed_us_);
    }
}






