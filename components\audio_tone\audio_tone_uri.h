#ifndef __AUDIO_TONEURI_H__
#define __AUDIO_TONEURI_H__

#include "stdio.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    TONE_DINGDING = 0,            // spiffs://spiffs/dingding.mp3
    TONE_OTA_SUCCESSED,           // spiffs://spiffs/ota_successed.mp3
    TONE_OTA_START,               // spiffs://spiffs/ota_start.mp3
    TONE_WELCOME,                 // spiffs://spiffs/welcome.mp3
    TONE_BUTTON,                  // spiffs://spiffs/button.mp3
    TONE_CHARGED,                 // spiffs://spiffs/charged.mp3
    TONE_NET_LINKED,              // spiffs://spiffs/net_linked.mp3
    TONE_BLUE_LINKED,             // spiffs://spiffs/blue_linked.mp3
    TONE_TOUCH_WELCOME,           // spiffs://spiffs/touch_welcome.mp3
    TONE_DEV_AUTH_SUCC,           // spiffs://spiffs/dev_auth_succ.mp3
    TONE_DEV_AUTH_FAILED,         // spiffs://spiffs/dev_auth_failed.mp3
    TONE_CON_SERVICE_TIMEOUT,     // spiffs://spiffs/con_service_timeout.mp3
    TONE_MAX                      // 枚举总数（可用于边界检查）
} tone_music_t;


const char * get_tone_url(tone_music_t index);
#ifdef __cplusplus
}
#endif

#endif
