/*
 * 示例：如何使用音频管道静音冲刷功能
 * 
 * 这个示例展示了如何在音频流突然断开时使用静音数据清除管道中的残留数据，
 * 防止下次播放时出现旧音频数据。
 */

#include "audio_processor.h"
#include "esp_log.h"

static const char *TAG = "AUDIO_FLUSH_EXAMPLE";

void example_audio_stream_with_flush(void)
{
    // 1. 创建音频播放器
    audio_player_t *player = create_audio_player();
    if (player == NULL) {
        ESP_LOGE(TAG, "Failed to create audio player");
        return;
    }

    // 2. 开始音频流播放
    ESP_LOGI(TAG, "Starting audio stream...");
    // 这里应该调用相应的播放函数启动流
    // player->stream_pipeline->player_pipeline_run(player->stream_pipeline, NULL);

    // 3. 模拟正常的音频数据写入
    ESP_LOGI(TAG, "Writing audio data...");
    uint8_t audio_data[1600] = {0x12, 0x34, 0x56, 0x78}; // 模拟音频数据
    
    // 正常写入音频数据
    if (player->stream_pipeline->player_pipeline_write) {
        player->stream_pipeline->player_pipeline_write(audio_data, sizeof(audio_data));
    }

    // 4. 模拟音频流突然断开的情况
    ESP_LOGW(TAG, "Audio stream interrupted! Flushing pipeline with silence...");
    
    // 使用静音数据冲刷管道，清除残留数据
    esp_err_t ret = audio_player_flush_pipeline_silence();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Pipeline flushed successfully with silence data");
    } else {
        ESP_LOGE(TAG, "Failed to flush pipeline with silence");
    }

    // 5. 停止播放器（这里会自动进行静音冲刷）
    ESP_LOGI(TAG, "Stopping audio stream...");
    // player->stream_pipeline->player_pipeline_stop(player->stream_pipeline);

    ESP_LOGI(TAG, "Audio flush example completed");
}

/*
 * 使用场景说明：
 * 
 * 1. 网络音频流中断时：
 *    当网络连接不稳定导致音频数据流中断时，调用 audio_player_flush_pipeline_silence()
 *    可以清除管道中的残留数据，避免下次连接恢复时播放旧的音频片段。
 * 
 * 2. 切换音频源时：
 *    在从一个音频源切换到另一个音频源之前，使用此函数确保管道清洁。
 * 
 * 3. 音频质量问题排查：
 *    当发现音频播放有杂音或异常时，可以尝试使用此函数清理管道。
 * 
 * 注意事项：
 * - 只有在管道处于运行状态时才能进行静音冲刷
 * - 冲刷过程会写入约250ms的静音数据
 * - 停止管道时会自动进行静音冲刷，通常不需要手动调用
 */
