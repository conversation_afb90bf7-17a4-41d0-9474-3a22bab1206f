/*
 * ESPRESSIF MIT License
 *
 * Copyright (c) 2022 <ESPRESSIF SYSTEMS (SHANGHAI) CO., LTD>
 *
 * Permission is hereby granted for use on all ESPRESSIF SYSTEMS products, in which case,
 * it is free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the Software is furnished
 * to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or
 * substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 */

static const char *TAG = "AUDIO_BOARD";

#include "esp_log.h"
#include <wifi_board.h>
#include <button.h>
#include "esp_log.h"
#include "board_def.h"
#include "audio_hal.h"
#include "led.h"
#include "circular_strip.h"
#include "system_reset.h"
#include "touch_button.h"
#include "power_manager.h"
#include "device_status_manager.h"

#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_vendor.h"
#include "esp_lcd_panel_ops.h"
#include "backlight.h"
#include "lvgl.h"
#include "esp_lvgl_port.h"
#include "drv8834.h"
#include "power_save_timer.h"

#include "esp_lcd_icna3306.h"
//#include "driver/i2c.h"
#include "driver/i2c_master.h"
#include "esp_lcd_touch_cst816s.h"
#include "font_awesome_symbols.h"




#define MMAP_LOTTIE_ASSETS_FILES           6
#define MMAP_LOTTIE_ASSETS_CHECKSUM        0xedf9

LV_FONT_DECLARE(font_puhui_16_4);
class Esp32S3MokeAIDeskBoard : public WifiBoard 
{
    private:
        Button reset_button_;
        TouchSensor touch_button_;
        SystemReset *reseter;
        PowerManager *power_manager_;
        audio_hal_handle_t codec_hal;
        audio_hal_handle_t adc_hal;
        PowerSaveTimer* power_save_timer_;
        esp_lcd_panel_io_handle_t panel_io = nullptr;
        esp_lcd_panel_io_handle_t tp_io_handle = NULL;
        esp_lcd_panel_handle_t panel = nullptr;
        lv_display_t *display = nullptr;
        lv_disp_drv_t disp_drv;
        lv_disp_draw_buf_t disp_buf; // 包含称为绘制缓冲区的内部图形缓冲区
        static SemaphoreHandle_t lvgl_mux;
        lv_obj_t *display_area;
    public:

    static void InitLVGLMux(void)
    {
        lvgl_mux = xSemaphoreCreateMutex();
        assert(lvgl_mux && "bsp_display_start must be called first");
    }
     void InitializeSpi() {
        const spi_bus_config_t buscfg = ICNA3306_PANEL_BUS_QSPI_CONFIG(LCD_PCLK_PIN,
                                                                 LCD_DATA0_PIN,
                                                                 LCD_DATA1_PIN,
                                                                 LCD_DATA2_PIN,
                                                                 LCD_DATA3_PIN,
                                                                 LCD_H_RES * LCD_V_RES * LCD_BIT_PER_PIXEL / 8);

        ESP_ERROR_CHECK(spi_bus_initialize(LCD_SPI_NUM, &buscfg, SPI_DMA_CH_AUTO));
    }

    static bool example_notify_lvgl_flush_ready(esp_lcd_panel_io_handle_t panel_io, esp_lcd_panel_io_event_data_t *edata, void *user_ctx)
    {
        lv_disp_drv_t *disp_driver = (lv_disp_drv_t *)user_ctx;
        lv_disp_flush_ready(disp_driver);
        return false;
    }
    void InitializeICNA3306Display() {

        ESP_LOGI(TAG, "Install panel IO");
        // 液晶屏控制IO初始化
        const esp_lcd_panel_io_spi_config_t io_config = ICNA3306_PANEL_IO_QSPI_CONFIG(LCD_CS_PIN, example_notify_lvgl_flush_ready, &disp_drv);
        inca3306_vendor_config_t vendor_config = {
            .flags = {
                .use_qspi_interface = 1,
            },
        };
        ESP_ERROR_CHECK(esp_lcd_new_panel_io_spi((esp_lcd_spi_bus_handle_t)LCD_SPI_NUM, &io_config, &panel_io));
        // 初始化液晶屏驱动芯片ICANA3306
        const esp_lcd_panel_dev_config_t panel_config = {
            .reset_gpio_num = LCD_RST_PIN,
            .rgb_ele_order = LCD_RGB_ELEMENT_ORDER_RGB,
            .bits_per_pixel = LCD_BIT_PER_PIXEL,
            .vendor_config = &vendor_config,
        };
        ESP_LOGI(TAG, "Install ICNA3306 panel driver");
        ESP_ERROR_CHECK(esp_lcd_new_panel_inca3306(panel_io, &panel_config, &panel));

        ESP_ERROR_CHECK(esp_lcd_panel_reset(panel));
        ESP_ERROR_CHECK(esp_lcd_panel_init(panel));
        ESP_ERROR_CHECK(esp_lcd_panel_disp_on_off(panel, true));
        ESP_LOGI(TAG, "Install LCD driver");
    }

    void InitializeTouchSrcreenSensor() {
        
    ESP_LOGI(TAG, "Initialize I2C bus");
    #if 0
    i2c_config_t i2c_conf = {};

    i2c_conf.mode = I2C_MODE_MASTER,
    i2c_conf.sda_pullup_en = GPIO_PULLUP_ENABLE;
    i2c_conf.scl_pullup_en = GPIO_PULLUP_ENABLE;
    i2c_conf.master.clk_speed = 400 * 1000;
    i2c_conf.sda_io_num = LCD_TOUCH_I2C_SDA_PIN;
    i2c_conf.scl_io_num = LCD_TOUCH_I2C_SCL_PIN;
    
    ESP_ERROR_CHECK(i2c_param_config(LCD_TOUCH_HOST, &i2c_conf));
    ESP_ERROR_CHECK(i2c_driver_install(LCD_TOUCH_HOST, i2c_conf.mode, 0, 0, 0));
    


    // I2C 总线配置
    i2c_master_bus_config_t bus_cfg = {};
    bus_cfg.i2c_port = I2C_NUM_0;              // 使用 I2C0
    bus_cfg.sda_io_num = LCD_TOUCH_I2C_SDA_PIN;           // SDA 引脚
    bus_cfg.scl_io_num = LCD_TOUCH_I2C_SCL_PIN;           // SCL 引脚
    bus_cfg.clk_source = I2C_CLK_SRC_DEFAULT;   // 使用默认时钟源
    bus_cfg.glitch_ignore_cnt = 7;              // 滤波计数（7 个时钟周期）
    bus_cfg.flags.enable_internal_pullup = true; // 启用内部上拉

    i2c_master_bus_handle_t bus_handle;      // 总线句柄
    ESP_ERROR_CHECK(i2c_new_master_bus(&bus_cfg, &bus_handle));

    const esp_lcd_panel_io_i2c_config_t tp_io_config = ESP_LCD_TOUCH_IO_I2C_CST816S_CONFIG();
    // Attach the TOUCH to the I2C bus
    ESP_ERROR_CHECK(esp_lcd_new_panel_io_i2c((esp_lcd_i2c_bus_handle_t)LCD_TOUCH_HOST, &tp_io_config, &tp_io_handle));
    #endif
    }

    /* 在 LVGL 中旋转屏幕时，旋转显示和触摸。 更新驱动程序参数时调用 */
static void example_lvgl_update_cb(lv_disp_drv_t *drv)
{
    esp_lcd_panel_handle_t panel_handle = (esp_lcd_panel_handle_t) drv->user_data;

    switch (drv->rotated) {
    case LV_DISP_ROT_NONE:
        // 旋转液晶显示屏
        esp_lcd_panel_swap_xy(panel_handle, false);
        esp_lcd_panel_mirror(panel_handle, true, false);
        // 旋转液晶触摸
        //esp_lcd_touch_set_mirror_y(tp, false);
        //esp_lcd_touch_set_mirror_x(tp, false);
        break;
    case LV_DISP_ROT_90:
        // 旋转液晶显示屏
        esp_lcd_panel_swap_xy(panel_handle, true);
        esp_lcd_panel_mirror(panel_handle, true, true);
        // 旋转液晶触摸
        //esp_lcd_touch_set_mirror_y(tp, false);
        //esp_lcd_touch_set_mirror_x(tp, false);
        break;
    case LV_DISP_ROT_180:
        // 旋转液晶显示屏
        esp_lcd_panel_swap_xy(panel_handle, false);
        esp_lcd_panel_mirror(panel_handle, false, true);
        // 旋转液晶触摸
        //esp_lcd_touch_set_mirror_y(tp, false);
        //esp_lcd_touch_set_mirror_x(tp, false);
        break;
    case LV_DISP_ROT_270:
        // 旋转液晶显示屏
        esp_lcd_panel_swap_xy(panel_handle, true);
        esp_lcd_panel_mirror(panel_handle, false, false);
        // 旋转液晶触摸
        //esp_lcd_touch_set_mirror_y(tp, false);
        //esp_lcd_touch_set_mirror_x(tp, false);
        break;
    }
}

static void example_lvgl_rounder_cb(struct _lv_disp_drv_t *disp_drv, lv_area_t *area)
{
    uint16_t x1 = area->x1;
    uint16_t x2 = area->x2;
    uint16_t y1 = area->y1;
    uint16_t y2 = area->y2;

    // round the start of coordinate down to the nearest 2M number
    area->x1 = (x1 >> 1) << 1;
    area->y1 = (y1 >> 1) << 1;
    // round the end of coordinate up to the nearest 2N+1 number
    area->x2 = ((x2 >> 1) << 1) + 1;
    area->y2 = ((y2 >> 1) << 1) + 1;
}

static void example_lvgl_flush_cb(lv_disp_drv_t *drv, const lv_area_t *area, lv_color_t *color_map)
{
    esp_lcd_panel_handle_t panel_handle = (esp_lcd_panel_handle_t) drv->user_data;
    const int offsetx1 = area->x1;
    const int offsetx2 = area->x2;
    const int offsety1 = area->y1;
    const int offsety2 = area->y2;

    // 将缓冲区的内容复制到显示的特定区域
    esp_lcd_panel_draw_bitmap(panel_handle, offsetx1, offsety1, offsetx2 + 1, offsety2 + 1, color_map);
}

    static void example_lvgl_port_task(void *arg)
    {
        ESP_LOGI(TAG, "Starting LVGL task");
        uint32_t task_delay_ms = 5;
        while (1) {
            // 由于 LVGL API 不是线程安全的，因此锁定互斥体
            if (example_lvgl_lock(-1)) {
                task_delay_ms = lv_timer_handler();
                // 释放互斥锁
                example_lvgl_unlock();
            }
            if (task_delay_ms > EXAMPLE_LVGL_TASK_MAX_DELAY_MS) {
                task_delay_ms = EXAMPLE_LVGL_TASK_MAX_DELAY_MS;
            } else if (task_delay_ms < EXAMPLE_LVGL_TASK_MIN_DELAY_MS) {
                task_delay_ms = EXAMPLE_LVGL_TASK_MIN_DELAY_MS;
            }
            vTaskDelay(pdMS_TO_TICKS(task_delay_ms));
        }
    }
   static  bool example_lvgl_lock(int timeout_ms)
    {
        assert(lvgl_mux && "bsp_display_start must be called first");

        const TickType_t timeout_ticks = (timeout_ms == -1) ? portMAX_DELAY : pdMS_TO_TICKS(timeout_ms);
        return xSemaphoreTake(lvgl_mux, timeout_ticks) == pdTRUE;
    }

    static void example_lvgl_unlock(void)
    {
        assert(lvgl_mux && "bsp_display_start must be called first");
        xSemaphoreGive(lvgl_mux);
    }
    static void example_increase_lvgl_tick(void *arg)
    {
        /* 告诉 LVGL 已经过去了多少毫秒*/
        lv_tick_inc(EXAMPLE_LVGL_TICK_PERIOD_MS);
    }

    void LVGLInit(void)
    {
        ESP_LOGI(TAG, "Initialize LVGL library");
        lv_init();

        //分配 LVGL 使用的绘制缓冲区
        // 建议选择绘制缓冲区的大小至少为屏幕大小的 1/10
        lv_color_t *buf1 = (lv_color_t *)heap_caps_malloc(LCD_H_RES * 30 * sizeof(lv_color_t), MALLOC_CAP_SPIRAM);
        assert(buf1);
        lv_color_t *buf2 = (lv_color_t *) heap_caps_malloc(LCD_H_RES * 30 * sizeof(lv_color_t), MALLOC_CAP_SPIRAM);
        assert(buf2);
        // 初始化 LVGL 绘制缓冲区
        lv_disp_draw_buf_init(&disp_buf, buf1, buf2, LCD_H_RES * 30);

        ESP_LOGI(TAG, "Register display driver to LVGL");
        lv_disp_drv_init(&disp_drv);
        disp_drv.hor_res = LCD_H_RES;
        disp_drv.ver_res = LCD_V_RES;
        disp_drv.flush_cb = example_lvgl_flush_cb;
        disp_drv.rounder_cb = example_lvgl_rounder_cb;
        disp_drv.drv_update_cb = example_lvgl_update_cb;
        disp_drv.draw_buf = &disp_buf;
        disp_drv.user_data = panel;
        lv_disp_t *disp = lv_disp_drv_register(&disp_drv);

        ESP_LOGI(TAG, "Install LVGL tick timer");
        // LVGL 的 Tick 接口（使用 esp_timer 生成 2ms 周期性事件）
        const esp_timer_create_args_t lvgl_tick_timer_args = {
        .callback = &example_increase_lvgl_tick,
        .name = "lvgl_tick"
        };
        esp_timer_handle_t lvgl_tick_timer = NULL;
        ESP_ERROR_CHECK(esp_timer_create(&lvgl_tick_timer_args, &lvgl_tick_timer));
        ESP_ERROR_CHECK(esp_timer_start_periodic(lvgl_tick_timer, EXAMPLE_LVGL_TICK_PERIOD_MS * 1000));
        InitLVGLMux();
        xTaskCreatePinnedToCore(
            example_lvgl_port_task,  // 任务函数
            "LVGL",                 // 任务名称
            EXAMPLE_LVGL_TASK_STACK_SIZE, // 堆栈大小
            NULL,                   // 参数
            EXAMPLE_LVGL_TASK_PRIORITY, // 优先级
            NULL,                   // 任务句柄
            1                       // 核心ID(0或1)
        );


        }
    void InitializePowerSaveTimer() {
        power_save_timer_ = new PowerSaveTimer(240, 60);
        power_save_timer_->OnEnterSleepMode([this]() {
            ESP_LOGI(TAG, "Enabling sleep mode");
            auto * led   = GetLed();
            auto * backlight = GetBacklight();
            struct StripColor high = {0, 0, 50};
            led->SetStaticColor(high);
            backlight->SetBrightness(1);
        });
        power_save_timer_->OnExitSleepMode([this]() {
            ESP_LOGI(TAG, "Disabling sleep mode");
            auto * backlight = GetBacklight();
            backlight->SetBrightness(100);
        });
        power_save_timer_->SetEnabled(true);
    }

     void InitializePowerManager() {
        power_manager_ = new PowerManager(POWER_CHARGER_PIN, BATTERY_READ_VOL_CHANEL);
        power_manager_->OnChargingStatusChanged([this](bool is_charging) {
            
            DeviceStateMsg_t msg = {STATE_BATT_CHARGING,  nullptr};
            auto &device_state_manager = DeviceStatusManger::GetInstance();
            

            if (is_charging) {
                device_state_manager.SetDeviceStatus(&msg);
                power_save_timer_->SetEnabled(false);
            } else {
                msg.state = STATE_BATT_DSCHARGING;
                device_state_manager.SetDeviceStatus(&msg);
                power_save_timer_->SetEnabled(true);
            }
        });
        power_manager_->OnBatteryLevelReport([this](uint32_t level) 
        {
            static uint32_t batt_level = 0;
            batt_level = level;
            DeviceStateMsg_t msg = {STATE_BATT_LEVEL_REPOART,  (void *)&batt_level};
            auto &device_state_manager = DeviceStatusManger::GetInstance();
            device_state_manager.SetDeviceStatus(&msg);
        });

        power_manager_->OnLowBatteryStatusChanged([this](uint8_t low) {

            DeviceStateMsg_t msg = {STATE_BATT_LOW, nullptr};
            auto &device_state_manager = DeviceStatusManger::GetInstance();
            device_state_manager.SetDeviceStatus(&msg);
            
        });
        
        }

        void InitializeButtons() {
        reset_button_.OnLongPress([this]() {
            auto &device_state_manager = DeviceStatusManger::GetInstance();
            static std::string str = "reset_button";
            ESP_LOGI(TAG, "Reset button long press");
            DeviceStateMsg_t msg = {STATE_BUTTON_LONG_PRESS, (void *)str.c_str()};
            device_state_manager.SetDeviceStatus(&msg);
            PowerOff();
        });

        reset_button_.OnDoubleClick([this]() {
            static std::string str = "reset_button";
            ESP_LOGD(TAG, "Reset button double click press");
            DeviceStateMsg_t msg = {STATE_BUTTON_DOUBLE_CLICK,  (void *)str.c_str()};
            auto &device_state_manager = DeviceStatusManger::GetInstance();
            device_state_manager.SetDeviceStatus(&msg);
            reseter->ResetNvsFlash();
            reseter->RestartInSeconds(3);
        });
        
        touch_button_.OnTouch([this]() {
            static std::string str = "touch_button";
            DeviceStateMsg_t msg = {STATE_BUTTON_CLICK, (void *)str.c_str()};
            auto &device_state_manager = DeviceStatusManger::GetInstance();
            device_state_manager.SetDeviceStatus(&msg);
        });
    }

    void InitializeSystemPowerPin(void)
        {
            gpio_config_t io_conf = {
                .pin_bit_mask = 1ULL << POWER_HOLD_PIN,  // 设置需要配置的 GPIO 引脚
                .mode = GPIO_MODE_OUTPUT,           // 设置为输出模式
                .pull_up_en = GPIO_PULLUP_DISABLE,  // 禁用上拉
                .pull_down_en = GPIO_PULLDOWN_DISABLE,  // 禁用下拉
                .intr_type = GPIO_INTR_DISABLE      // 禁用中断
            };
            gpio_config(&io_conf);  // 应用配置
        }
        void PowerOn(void)
        {
            gpio_set_level(POWER_HOLD_PIN, 1);
        }
        void PowerOff(void)
        {
            gpio_set_level(POWER_HOLD_PIN, 0);
        }


        std:: string GetBoardName(void) override
        {
            return "mokeai_desk";
        }
        audio_hal_handle_t CodecInit(void)
        {
            audio_hal_codec_config_t audio_codec_cfg = AUDIO_CODEC_DEFAULT_CONFIG();
            audio_codec_cfg.i2s_iface.samples = AUDIO_HAL_16K_SAMPLES;
            audio_hal_handle_t codec_hal = audio_hal_init(&audio_codec_cfg, &AUDIO_CODEC_ES8156_DEFAULT_HANDLE);
            AUDIO_NULL_CHECK(TAG, codec_hal, return NULL);
            return codec_hal;
        }
        audio_hal_handle_t AdcInit(void)
        {
            audio_hal_codec_config_t audio_codec_cfg = AUDIO_CODEC_DEFAULT_CONFIG();
            
            audio_hal_handle_t adc_hal = NULL;
            adc_hal = audio_hal_init(&audio_codec_cfg, &AUDIO_CODEC_ES7210_DEFAULT_HANDLE);
            AUDIO_NULL_CHECK(TAG, adc_hal, return NULL);
            return adc_hal;
        }
        
        virtual Led* GetLed() override 
        {
            static CircularStrip led(BUILTIN_LED_PIN, BUILTIN_LED_NUM);
            return &led;
        }
        Esp32S3MokeAIDeskBoard():WifiBoard(), 
        reset_button_(BOOT_BUTTON_PIN),
        touch_button_(TOUCH_BUTTON_PIN, 0.05f)
        {
            vTaskDelay(1000 / portTICK_PERIOD_MS);
            InitializeSystemPowerPin();
            PowerOn();
            InitializePowerSaveTimer();
            InitializePowerManager();
            InitializeSpi();
            InitializeICNA3306Display();
            InitializeTouchSrcreenSensor();
            LVGLInit();
            if (example_lvgl_lock(-1)) {
            // 初始化样式对象
                static lv_style_t style_main;
                lv_style_init(&style_main);
                lv_style_set_bg_opa(&style_main, LV_OPA_COVER);

                // 获取当前屏幕
                lv_obj_t *screen = lv_scr_act();
                lv_obj_add_style(screen, &style_main, LV_PART_MAIN);

                // 创建顶部状态栏
                lv_obj_t *status_bar = lv_label_create(screen);
                lv_obj_set_style_text_font(status_bar, &font_puhui_16_4, 0);
                lv_label_set_text(status_bar, "MoKeAI桌面机器人");
                lv_obj_set_pos(status_bar, 10, 10);
                lv_obj_set_size(status_bar, 220, 20);
                lv_obj_set_style_text_align(status_bar, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN);

                // 创建主内容区域容器
                lv_obj_t *content = lv_obj_create(screen);
                lv_obj_set_pos(content, 10, 40);
                lv_obj_set_size(content, 220, LCD_V_RES - 60);
                lv_obj_set_flex_flow(content, LV_FLEX_FLOW_COLUMN);
                // 语音识别区域
                display_area = lv_textarea_create(content);
                // 设置文本框样式（与原标签保持一致）
                lv_obj_set_style_text_font(display_area, &font_puhui_16_4, 0);
                lv_obj_set_style_text_align(display_area, LV_TEXT_ALIGN_LEFT, 0);
                // 配置文本框行为
                lv_textarea_set_one_line(display_area, false);  // 允许多行
                // 设置大小和位置（与原标签相同）
                lv_obj_set_size(display_area, 210, 210);
                lv_obj_set_scrollbar_mode(display_area, LV_SCROLLBAR_MODE_OFF);
                ESP_LOGI(TAG, "screen style set.");
                example_lvgl_unlock();
            }

            reseter = new SystemReset();
            codec_hal = CodecInit();
            adc_hal = AdcInit();
            audio_hal_ctrl_codec(codec_hal, AUDIO_HAL_CODEC_MODE_DECODE, AUDIO_HAL_CTRL_START);
           
            InitializeButtons();
            // LCD Init
            
            ESP_LOGI(TAG, "Initializing Esp32S3MokeAIDeskBoard ");       
        }
        void Setvolume(int vol)
        {
            audio_hal_set_volume(codec_hal, vol);
            ESP_LOGI(TAG, "Setvolume %d", vol);
        }
        void Getvolume(int *vol)
        {
            audio_hal_get_volume(codec_hal, vol);
            ESP_LOGD(TAG, "Getvolume %d", *vol);
        }
        virtual Backlight* GetBacklight() override {
            static NoBacklight backlight;
            return &backlight;
        }
        void SetDisEmoji(void *lv_obj, const char *img_path)
        {

        }

        void SetDisText(void *lv_obj, const char* text)
        {
            if (example_lvgl_lock(-1))
            {
                static void *last_obj;
                if (text)
                    ESP_LOGI(TAG, "text: %s\n", text);
                if (lv_obj == (void *)0)
                {
                    last_obj = lv_obj;
                    lv_textarea_add_text(display_area, text);
                }
                else if (lv_obj == (void *)1)
                {
                    if (last_obj == (void *)0)
                    {
                        last_obj = lv_obj;
                        lv_textarea_set_text(display_area, "");
                    }
                    lv_textarea_add_text(display_area, text);
                }
                else if (lv_obj == (void *)2)
                {
                    lv_textarea_set_text(display_area, "");
                }
                else
                {

                }
                if (text)
                    free((void *)text);
                example_lvgl_unlock();
            }
        }
        virtual void SetPowerSaveMode(bool enabled) override {
            if (!enabled) {
                power_save_timer_->WakeUp();
            }
            WifiBoard::SetPowerSaveMode(enabled);
        }
        virtual Stepper* GetStpper(uint32_t stpper_idx) override 
        {
            
            static drv8834 v_stpper(DRV8834_H_DIR_PIN,DRV8834_H_STEP_PIN, DRV8834_H_EN_PIN);
            static drv8834 h_stpper(DRV8834_V_DIR_PIN,DRV8834_V_STEP_PIN, DRV8834_V_EN_PIN);

            if (stpper_idx == 0)
            {
                ESP_LOGI(TAG, "GetStpper v_stpper");
                return &v_stpper;
            }
            else
            {
                ESP_LOGI(TAG, "GetStpper h_stpper");
                return &h_stpper;
            }

        }
};
SemaphoreHandle_t Esp32S3MokeAIDeskBoard::lvgl_mux = nullptr;
DECLARE_BOARD(Esp32S3MokeAIDeskBoard)
    
