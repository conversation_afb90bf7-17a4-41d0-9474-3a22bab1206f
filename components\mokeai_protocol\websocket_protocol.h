#ifndef _WEBSOCKET_PROTOCOL_H_
#define _WEBSOCKET_PROTOCOL_H_


#include "protocol.h"

#include <web_socket.h>
#include <freertos/FreeRTOS.h>
#include <freertos/event_groups.h>
#include <freertos/semphr.h>

#define WEBSOCKET_PROTOCOL_SERVER_HELLO_EVENT (1 << 0)
#define WEBSOCKET_PROTOCOL_SERVER_AUTH_EVENT (1 << 1)
#define WEBSOCKET_PROTOCOL_SERVER_CONNECTED_EVENT (1 << 2)


#define PROT_SUCCESSED_CODE    200UL
#define PROT_DEV_CERT_SUCCESS  201UL
#define PROT_ERROR_CODE        500UL
#define PROT_TIMEOUT_CODE      501UL
#define PROT_DEV_START_CODE    401UL
#define PROT_DEV_STOP_CODE     502UL
#define PROT_DEV_AUTHING_CODE  401UL


#define MAX_CACHE_SIZE 3

typedef struct {
    char* buffer[MAX_CACHE_SIZE];
    int current_index;  // 当前写入位置
    int count;          // 当前缓存帧数
}ServerMsgCache_t;


class WebsocketProtocol : public Protocol {
public:
    WebsocketProtocol();
    ~WebsocketProtocol();

    void Start() override;
    void SendAudio(const std::vector<uint8_t>& data) override;
    bool OpenAudioChannel() override;
    void CloseAudioChannel() override;
    bool DeviceAuthenticate(void) override;
    enum connection_state GetConnectionState() const override;
    enum authentication_state GetAuthenticationState() const override;
    bool IsAudioChannelOpened() const override;
    void SendStartListening(ListeningMode mode) override;
    void SendStopListening() override;
    void SendWakeWordDetected(const std::string& wake_word) override;
    void SendTouchDetect(void) override;
    void SendManualExit() override;
    void SendAutoExit() override;
    void SetServerRetCode(uint32_t code) { server_ret_code_ = code;} 
    uint32_t GetServerRetCode() const { return server_ret_code_; } 
    std::string GetServerMsgType() const {return msg_type_;}
    
private:
    std::string msg_type_;
    std::string url_;
    std::string task_id_;
    std::string prev_task_id_;
    std::string topic_id_;
    ServerMsgCache_t tts_cache_;
    EventGroupHandle_t event_group_handle_;
    SemaphoreHandle_t channel_semaphore_;
    TaskHandle_t ping_task_handle;
    uint32_t ping_intervalms_ = 60000;
    WebSocket* websocket_ = nullptr;
    uint32_t server_ret_code_ = PROT_SUCCESSED_CODE; // Server return code
    void ParseServerHello(const cJSON* root);
    void SendText(const std::string& text) override;
    void PingTimerCallback(void);
    void ServerMsgCacheAddItem(ServerMsgCache_t* cache, const char* text);
    void ServerMsgCacheInit(ServerMsgCache_t* cache);
    char* ServerMsgCacheFlush(ServerMsgCache_t* cache);
};

#endif
