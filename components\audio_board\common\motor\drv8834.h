#ifndef _DRV8834_DRIVER_H
#define _DRV8834_DRIVER_H

#include "stepper.h"
#include "driver/gpio.h"

class drv8834 : public Stepper{
public:
    
    drv8834(gpio_num_t dir_pin, gpio_num_t step_pin, gpio_num_t enable_pin);
    void set_direction(bool direction) override ;
    void enable() override;
    void disable() override;
    void step(uint32_t steps) override;
    drv8834() {};
    ~drv8834() {};
    
private:

    uint32_t speed_us_ = 100;  // 时间越短，旋转速度越快,单位us
    void delay_us(uint32_t us);

    gpio_num_t dir_pin;
    gpio_num_t step_pin;
    gpio_num_t enable_pin;
};




#endif