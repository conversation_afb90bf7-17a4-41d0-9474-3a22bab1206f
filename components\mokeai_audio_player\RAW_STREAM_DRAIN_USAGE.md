# Raw Stream 数据消耗功能

## 问题解决

当音频流突然断开时，raw stream 的输出缓冲区中会残留压缩音频数据。这些残留数据会在下次播放时被播放出来，导致音频异常。

## 解决方案

实现了 `_drain_raw_stream_data()` 函数，通过直接读取并丢弃 raw stream 输出缓冲区中的所有数据来解决这个问题。

## 核心功能

1. **自动消耗**：在停止管道时自动消耗 raw stream 中的残留数据
2. **手动消耗**：提供 `audio_player_flush_pipeline_silence()` 接口供手动调用

## 使用方法

### 自动使用（推荐）
停止音频播放时会自动消耗残留数据：
```c
player->stream_pipeline->player_pipeline_stop(player->stream_pipeline);
// 会自动调用 _drain_raw_stream_data() 消耗残留数据
```

### 手动使用
在音频流中断但不停止播放器的情况下：
```c
// 检测到音频流中断
if (audio_stream_interrupted) {
    esp_err_t ret = audio_player_flush_pipeline_silence();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Raw stream drained successfully");
    }
}
```

## 技术实现

- 直接访问 raw stream 的输出 ring buffer
- 使用 `rb_bytes_available()` 检查可用数据量
- 使用 `rb_read()` 读取并丢弃数据
- 循环直到缓冲区完全清空

## 日志输出

正常工作时会看到：
```
I (xxx) AUDIO_PLAYER: Draining raw stream data...
I (xxx) AUDIO_PLAYER: Raw stream has 2048 bytes available
D (xxx) AUDIO_PLAYER: Drained 1024 bytes from raw stream (total: 1024)
D (xxx) AUDIO_PLAYER: Drained 1024 bytes from raw stream (total: 2048)
I (xxx) AUDIO_PLAYER: Successfully drained 2048 bytes from raw stream
```

## 优势

- **直接有效**：直接消耗残留数据，不依赖静音填充
- **资源友好**：不需要额外的静音数据缓冲区
- **可靠性高**：确保残留数据被完全清除
