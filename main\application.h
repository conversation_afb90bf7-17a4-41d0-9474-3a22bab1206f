#ifndef _APPLICATION_H_
#define _APPLICATION_H_

#include <freertos/FreeRTOS.h>
#include <freertos/event_groups.h>
#include <freertos/task.h>
#include <esp_timer.h>
#include <string>
#include <mutex>
#include <list>
#include "protocol.h"
#include "audio_processor.h"
#include "mokeai_wakeup.h"
#include "ota.h"
#include <system_reset.h>
#include "audio_ring_buffer.h"
#include <math.h>
#include <stdbool.h>

#define WAKEUP_REC_READING          (1 << 0)
#define VAD_STARTD                  (1 << 1)
#define VAD_STOPD                   (1 << 2)
#define REPORT_DEV_LSB_INFO         (1 << 3)
#define READ_DEV_LSB_INFO           (1 << 4)

// defines for 4G boards
#define EARTH_RADIUS 6371000.0  // 地球半径（米）
#define DEG_TO_RAD (M_PI / 180.0)
#define DISTANCE_THRESHOLD 100.0  // 100米阈值

typedef struct {
    double lat; // 纬度
    double lon; // 经度
} Coordinate;

#define AUDIO_RING_BUFFER_SIZE      (20)
#define AUDIO_RING_BUFFER_ITEM_SIZE (1024)

typedef enum {
    DISPLAY_STATE_WAKEUP,          // 唤醒中
    DISPLAY_STATE_SLEEPING,        // 休眠中
    DISPLAY_STATE_LISTENING,       // 聆听中
    DISPLAY_STATE_THINKING,        // 思考中
    DISPLAY_STATE_NETWORK_CONFIG,  // 网络配置中
    DISPLAY_STATE_NETWORK_CONNECTING,   // 网络连接中
    DISPLAY_STATE_BAT_LOW,              // 电池电量低
} DeviceDisPlayState;

typedef struct {
    DeviceDisPlayState state;
    void *state_data;
}DeviceDisplayMsg;


class Application {
public:
    static Application& GetInstance() {
        static Application instance;
        return instance;
    }
    // 删除拷贝构造函数和赋值运算符
    Application(const Application&) = delete;
    Application& operator=(const Application&) = delete;

    AudioRingBuffer *audio_cache = new AudioRingBuffer(AUDIO_RING_BUFFER_SIZE, AUDIO_RING_BUFFER_ITEM_SIZE);
    FILE *audio_file = nullptr;
    FILE *audio_file_1 = nullptr;
    std::unique_ptr<Protocol> protocol_;
    bool is_dev_sleep = true;
    char * lbs_report_msg = nullptr;
    void Start();
    void voice_read_task();
    void voice_send_task();
    void device_status_mon_task();
    void device_lbs_read_task();
    void device_lbs_report_task();
    void device_res_monitor_task();
    bool check_device_location_distance(const float distance, const Coordinate *point1, const Coordinate *point2);
    void display_task();
    void vad_open_timer();
    EventGroupHandle_t GetEventGroupHandle() {
        return event_group_handle_;
    }
    audio_player_t * GetAudioPlayer() {
        return player_;
    }
    // 播放本地音频
    void PlaySound(const char *url)
    {
        if (player_ && player_->tone_pipeline)
            player_->tone_pipeline->player_pipeline_run(player_->tone_pipeline, url);
    }
    void CloseAudioPlayerStreamPIPE(void)
    {
        //gpio_set_level(PA_ENABLE_GPIO, 0);
        if (player_)
            player_->stream_pipeline->player_pipeline_stop(player_->stream_pipeline);
    }
    void OpenAudioPlayerStreamPIPE(void)
    {
        //gpio_set_level(PA_ENABLE_GPIO, 1);
        if (player_)
            player_->stream_pipeline->player_pipeline_run(player_->stream_pipeline, nullptr);
    } 
    
private:
    Ota ota_;
    audio_player_t *player_;
    struct wakeup_recorder *recorder_;
    EventGroupHandle_t event_group_handle_;
    SystemReset reseter_;
    std::mutex audio_cache_mutex_;
    xQueueHandle display_queue_;
    esp_timer_handle_t timer_handle_ = nullptr;
    Application();
    ~Application();
    void Reboot(void) { reseter_.RestartInSeconds(3);}
    void CheckNewVersion();

};

#endif // _APPLICATION_H_
