set(requires
    audio_board
    mokeai_audio_player
    audio_tone
    mokeai_protocol
    mokeai_wakeup
    audio_recorder
    lvgl__lvgl
    device_status_manager
 )

file(GLOB_RECURSE DUERAPP_SRCS 
    "main.cpp"
    "application.cc"
    "ota.cc"
    "audio_ring_buffer.cc"
    )
set(COMPONENT_ADD_INCLUDEDIRS
    "./"
    )
# 配置组件注册（保留原有依赖）
idf_component_register(
    SRCS ${DUERAPP_SRCS}
    INCLUDE_DIRS ${COMPONENT_ADD_INCLUDEDIRS} 
    REQUIRES ${requires}
)

spiffs_create_partition_image(spiffs_data ../components/audio_tone/spiffs FLASH_IN_PROJECT)
