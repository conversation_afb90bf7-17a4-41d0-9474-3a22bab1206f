/*
 * ESPRESSIF MIT License
 *
 * Copyright (c) 2025 <ESPRESSIF SYSTEMS (SHANGHAI) CO., LTD>
 *
 * Permission is hereby granted for use on all ESPRESSIF SYSTEMS products, in which case,
 * it is free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the Software is furnished
 * to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or
 * substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 */

#define SILENCE_FRAME_SIZE 1600  // 假设 16-bit, 16kHz, mono, 50 ms → 1600 bytes
static uint8_t silence_frame[SILENCE_FRAME_SIZE] = {0};
 
#include <string.h>

#include "esp_log.h"
#include "esp_timer.h"
#include "esp_check.h"
#include "sdkconfig.h"
#include "audio_element.h"
#include "raw_stream.h"
#include "filter_resample.h"
#include "audio_mem.h"
#include "audio_thread.h"
#include "board.h"
#include "filter_resample.h"
#include "raw_stream.h"
#include "spiffs_stream.h"
#include "audio_common.h"
#include "i2s_stream.h"
#include "mp3_decoder.h"
#include "wav_decoder.h"
#include "audio_processor.h"
#include "audio_pipeline.h"
#include "periph_spiffs.h"
#include "periph_sdcard.h"
#include "esp_peripherals.h"
#include  "opus_decoder.h"
static const char *TAG = "audio player processor";

//#define DEBUG_VOICE_REC
static audio_player_t *s_audio_player;


#define audio_pipe_safe_free(x, fn) do { \
    if (x) {                             \
        fn(x);                           \
        x = NULL;                        \
    }                                    \
} while (0)

static esp_err_t _player_audio_hal_get_volume(int *volume)
{
    if (s_audio_player->audio_hal != NULL)
        return audio_hal_get_volume(s_audio_player->audio_hal, volume);
    else
        return ESP_FAIL;
}



static esp_err_t _player_audio_hal_set_volume(int volume)
{
    ESP_RETURN_ON_FALSE(s_audio_player->audio_hal != NULL, ESP_FAIL, TAG, "audio_hal not initialized");
    return audio_hal_set_volume(s_audio_player->audio_hal, volume);
}

static esp_err_t _player_i2s_write_cb(audio_element_handle_t self, char *buffer, int len, TickType_t ticks_to_wait, void *context)
{
    s_audio_player->stream_pipeline->audio_frame_cnt++;
    return audio_element_output(s_audio_player->stream_pipeline->i2s, buffer, len);
}

static esp_err_t _player_write_nop_cb(audio_element_handle_t self, char *buffer, int len, TickType_t ticks_to_wait, void *context)
{
    return len;
}

static inline size_t _write_data_nop(void *data, size_t len)
{
    //return raw_stream_write(pip->raw, (char *)data, len);
    ESP_LOGI(TAG, "write data nop");
    return 0;
}
static audio_element_handle_t _create_decoder_stream(void)
{
    audio_element_handle_t decoder_stream = NULL;

    ESP_LOGI(TAG, "Create mp3 decoder");
    mp3_decoder_cfg_t mp3_dec_cfg = DEFAULT_MP3_DECODER_CONFIG();
    mp3_dec_cfg.out_rb_size = 4 * 1024;
    decoder_stream = mp3_decoder_init(&mp3_dec_cfg);
    return decoder_stream;
}
static audio_element_handle_t _create_raw_stream(void)
{
    audio_element_handle_t raw_stream = NULL;
    raw_stream_cfg_t raw_cfg = RAW_STREAM_CFG_DEFAULT();
    raw_cfg.type = AUDIO_STREAM_READER;
    // 增加输出缓冲区大小
    raw_cfg.out_rb_size = 4 * 1024; // 增加缓冲区大小
    raw_stream = raw_stream_init(&raw_cfg);
    return raw_stream;
}
static audio_element_handle_t _create_spiffs_stream(void)
{
    audio_element_handle_t spiffs_stream = NULL;
    spiffs_stream_cfg_t spiffs_cfg = SPIFFS_STREAM_CFG_DEFAULT();
    spiffs_cfg.type = AUDIO_STREAM_READER;
    spiffs_cfg.out_rb_size = 1024; 
    spiffs_cfg.buf_sz = 2 * 1024;
    spiffs_stream = spiffs_stream_init(&spiffs_cfg);
    return spiffs_stream;
}

static audio_element_handle_t _create_tone_i2s_stream(void)
{
    audio_element_handle_t i2s_stream = NULL;
    i2s_stream_cfg_t i2s_cfg = I2S_STREAM_CFG_DEFAULT_WITH_PARA(I2S_NUM_0, 16000, 32, AUDIO_STREAM_WRITER);
    i2s_cfg.out_rb_size = 2 * 1024;
    i2s_cfg.need_expand = false;
    i2s_cfg.buffer_len = 1416;
    //i2s_cfg.chan_cfg.dma_desc_num = 6;
    i2s_stream = i2s_stream_init(&i2s_cfg);
    return i2s_stream;
}

audio_element_handle_t _create_i2s_stream(bool enable_task)
{
    audio_element_handle_t i2s_stream = NULL;
    i2s_stream_cfg_t i2s_cfg = I2S_STREAM_CFG_DEFAULT_WITH_PARA(I2S_NUM_0, 16000, 32, AUDIO_STREAM_WRITER);
    if (enable_task == false) {
        i2s_cfg.task_stack = -1;
    }
    i2s_cfg.out_rb_size = 8 * 1024;
    i2s_cfg.need_expand = true;
    i2s_cfg.expand_src_bits = 16;
    i2s_cfg.buffer_len = 1416;
    i2s_cfg.chan_cfg.dma_desc_num = 6;
    i2s_stream = i2s_stream_init(&i2s_cfg);
    return i2s_stream;
}

static audio_element_handle_t _create_ch1_to_ch2_rsp_stream() // create_audio_player_rsp_stream
{
    audio_element_handle_t filter = NULL;
    rsp_filter_cfg_t filter_cfg = DEFAULT_RESAMPLE_FILTER_CONFIG();
    filter_cfg.src_ch = 1;
    filter_cfg.src_rate = 16000;
    filter_cfg.dest_ch = 2;
    filter_cfg.dest_rate = 32000;
    filter_cfg.stack_in_ext = true;
    filter_cfg.task_core = 1;
    filter_cfg.complexity = 5;
    filter = rsp_filter_init(&filter_cfg);
    return filter;
}
static inline size_t _write_data_to_pip(void *data, size_t len)
{
    struct player_pipeline* pip = s_audio_player->stream_pipeline;

    return raw_stream_write(pip->raw, (char *)data, len);
}

static esp_err_t _player_pipeline_run(struct player_pipeline *player_pipeline, const char *uri)
{

    ESP_RETURN_ON_FALSE(player_pipeline != NULL, ESP_FAIL, TAG, "player pipeline not initialized");

    if (player_pipeline->state == PIPE_STATE_RUNNING) 
    {
        ESP_LOGW(TAG, "player pipe %d is already running state", player_pipeline->type);
        return ESP_OK;
    }

    ESP_LOGI(TAG, "player pipe start running");
    if (player_pipeline->type == STREAM_MP3)
    {
        player_pipeline->player_pipeline_write = _write_data_to_pip;
        ESP_LOGI(TAG, "stream pip is running.");
        audio_element_set_write_cb(player_pipeline->rsp, _player_i2s_write_cb, NULL);
        audio_pipeline_run(player_pipeline->pipeline);
    }
    else
    {
        ESP_LOGI(TAG, "audio_tone_play: %s", uri);
        audio_element_set_uri(player_pipeline->spiffs, uri);
        audio_pipeline_run(player_pipeline->pipeline);
    }
    player_pipeline->state = PIPE_STATE_RUNNING;
    ESP_LOGI(TAG, "running player_pipeline->state = %d", player_pipeline->state);
    return ESP_OK;
}

static esp_err_t _player_pipeline_stop(struct player_pipeline *player_pipeline)
{
    AUDIO_MEM_CHECK(TAG, player_pipeline, return ESP_FAIL);
    ESP_RETURN_ON_FALSE(player_pipeline != NULL, ESP_FAIL, TAG, "player pipeline not initialized");
    
    if (player_pipeline->state == PIPE_STATE_IDLE) {

        ESP_LOGW(TAG, "player pipe is idle state");
        return ESP_FAIL;
    }
    ESP_LOGD(TAG, "player pipe stop running type = %d", player_pipeline->type);
    
    if (player_pipeline->type == STREAM_MP3)
    {
        player_pipeline->player_pipeline_write = _write_data_nop;
        audio_element_set_write_cb(player_pipeline->rsp, _player_write_nop_cb, NULL);
        audio_pipeline_stop(player_pipeline->pipeline);
        audio_pipeline_wait_for_stop(player_pipeline->pipeline);
        audio_pipeline_terminate(player_pipeline->pipeline);
        audio_pipeline_reset_ringbuffer(player_pipeline->pipeline);
        audio_pipeline_reset_elements(player_pipeline->pipeline);
    }
    else
    {
        audio_pipeline_stop(player_pipeline->pipeline);
        audio_pipeline_wait_for_stop(player_pipeline->pipeline);
        audio_pipeline_terminate(player_pipeline->pipeline);
        audio_pipeline_reset_ringbuffer(player_pipeline->pipeline);
        audio_pipeline_reset_elements(player_pipeline->pipeline);
    }
    player_pipeline->state = PIPE_STATE_IDLE;
    ESP_LOGD(TAG, "stop player_pipeline->state = %d", player_pipeline->state);
    return ESP_OK;
}






static void audio_player_state_task(void *arg)
{

    audio_player_t *player = (audio_player_t *) arg;
    audio_event_iface_handle_t evt = (audio_event_iface_handle_t) player->evt_iface;
    player->running = true;
    while (player->running)
    {
        audio_event_iface_msg_t msg;
        esp_err_t ret = audio_event_iface_listen(evt, &msg, portMAX_DELAY);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "[ * ] Event interface error : %d", ret);
            continue;
        }
        ESP_LOGD(TAG, "msg.cmd = %d, msg.source = %p stream_i2s = %p, tone_steam = %p", msg.cmd, msg.source, player->stream_pipeline->i2s, player->tone_pipeline->i2s);

        if (msg.source_type == AUDIO_ELEMENT_TYPE_ELEMENT && msg.source == (void *) player->stream_pipeline->rsp
            && msg.cmd == AEL_MSG_CMD_REPORT_STATUS
            && (((int)msg.data == AEL_STATUS_STATE_RUNNING))) 
        {
             player->stream_pipeline->state = PIPE_STATE_RUNNING;
             ESP_LOGI(TAG, "[ * ] MP3 Stream PIP Running event received");
        }
        if (msg.source_type == AUDIO_ELEMENT_TYPE_ELEMENT && msg.source == (void *) player->stream_pipeline->rsp
            && msg.cmd == AEL_MSG_CMD_REPORT_STATUS
            && (((int)msg.data == AEL_STATUS_STATE_STOPPED) || ((int)msg.data == AEL_STATUS_STATE_FINISHED))) {
            ESP_LOGI(TAG, "[ * ] MP3 Stream PIP Stop event received");
            //_player_pipeline_stop(player->stream_pipeline);
        }
        if (msg.source_type == AUDIO_ELEMENT_TYPE_ELEMENT && msg.source == (void *) player->tone_pipeline->i2s
            && msg.cmd == AEL_MSG_CMD_REPORT_STATUS
            && (((int)msg.data == AEL_STATUS_STATE_STOPPED) || ((int)msg.data == AEL_STATUS_STATE_FINISHED))) {
            ESP_LOGI(TAG, "[ * ] TONE Stream PIP Stop event received");
            _player_pipeline_stop(player->tone_pipeline);
        }
        
    }
    vTaskDelete(NULL);
}


static esp_err_t  _audio_pipeline_init(player_pipeline_t *_pipeline) 
{

    ESP_LOGI(TAG, "Create audio pipeline ");
    audio_pipeline_cfg_t pipeline_cfg = DEFAULT_AUDIO_PIPELINE_CONFIG();
    _pipeline->pipeline = audio_pipeline_init(&pipeline_cfg);
    AUDIO_MEM_CHECK(TAG, _pipeline->pipeline, goto _exit_open);

    if (_pipeline->type == STREAM_MP3)
    {
        ESP_LOGI(TAG, "Create audio streams for STREAM_MP3");
        _pipeline->raw = _create_raw_stream();
        AUDIO_MEM_CHECK(TAG, _pipeline->raw, goto _exit_open);
        _pipeline->decoder = _create_decoder_stream();
        AUDIO_MEM_CHECK(TAG, _pipeline->decoder, goto _exit_open);
        _pipeline->i2s = _create_i2s_stream(false);
        AUDIO_MEM_CHECK(TAG, _pipeline->i2s, goto _exit_open);
    
        _pipeline->rsp = _create_ch1_to_ch2_rsp_stream();
        AUDIO_MEM_CHECK(TAG, _pipeline->rsp, goto _exit_open); 
        ESP_LOGI(TAG, "Register all elements to  STREAM_MP3 pipeline");
        audio_pipeline_register(_pipeline->pipeline, _pipeline->raw, "stream_raw");
        audio_pipeline_register(_pipeline->pipeline, _pipeline->decoder, "stream_rdec");
        audio_pipeline_register(_pipeline->pipeline, _pipeline->rsp, "stream_rsp");
        //audio_pipeline_register(_pipeline->pipeline, _pipeline->i2s, "i2s");
        ESP_LOGI(TAG, "Link playback element together raw-->audio_decoder-->rsp-->i2s_stream-->[codec_chip]");
        audio_element_set_write_cb(_pipeline->rsp, _player_write_nop_cb, NULL);
        //const char *link_tag[4] = {"stream_raw","stream_rdec", "stream_rsp", "i2s"};
        
        const char *link_tag[3] = {"stream_raw","stream_rdec", "stream_rsp"};
        //audio_pipeline_link(_pipeline->pipeline, &link_tag[0], 4);
        audio_pipeline_link(_pipeline->pipeline, &link_tag[0], 3);
        // set listener to listen event from audio pipeline

    }
    else if (_pipeline->type == TONE)
    {
        ESP_LOGI(TAG, "Create audio streams for TONE");
        _pipeline->spiffs = _create_spiffs_stream();
        AUDIO_MEM_CHECK(TAG, _pipeline->spiffs, goto _exit_open);
        _pipeline->i2s    = _create_tone_i2s_stream();
        AUDIO_MEM_CHECK(TAG, _pipeline->i2s, goto _exit_open);
        _pipeline->decoder = _create_decoder_stream();
        AUDIO_MEM_CHECK(TAG, _pipeline->decoder, goto _exit_open);
        _pipeline->rsp = _create_ch1_to_ch2_rsp_stream();
        AUDIO_MEM_CHECK(TAG, _pipeline->rsp, goto _exit_open);

        ESP_LOGI(TAG, "Register all elements to TONE pipeline");
        audio_pipeline_register(_pipeline->pipeline, _pipeline->i2s, "tone_i2s");
        audio_pipeline_register(_pipeline->pipeline, _pipeline->decoder, "tone_dec");
        audio_pipeline_register(_pipeline->pipeline, _pipeline->spiffs, "tone_spiffs");
        audio_pipeline_register(_pipeline->pipeline, _pipeline->rsp, "tone_rsp");

        ESP_LOGI(TAG, "Link playback element together spiffs---->audio_decoder-->i2s_stream-->[codec_chip]");
        const char *link_tag[4] = {"tone_spiffs", "tone_dec", "tone_rsp" , "tone_i2s"};
        audio_pipeline_link(_pipeline->pipeline, &link_tag[0], 4);
    }
    else
    {
        ESP_LOGE(TAG, "audio pipeline type not support");
        return ESP_FAIL;
    }
    ESP_LOGI(TAG, "Link playback element Success.\n");
    return ESP_OK;
    
_exit_open:
    audio_pipe_safe_free(_pipeline->rsp, audio_element_deinit);
    audio_pipe_safe_free(_pipeline->raw, audio_element_deinit);
    audio_pipe_safe_free(_pipeline->decoder, audio_element_deinit);
    audio_pipe_safe_free(_pipeline->i2s, audio_element_deinit);
    audio_pipe_safe_free(_pipeline->spiffs, audio_element_deinit);
    audio_pipe_safe_free(_pipeline->pipeline, audio_pipeline_deinit);
    return ESP_FAIL;
}

audio_player_t * create_audio_player(void)
{
    esp_err_t ret = ESP_OK;

    ESP_RETURN_ON_FALSE(s_audio_player == NULL, ESP_FAIL, TAG, "player already initialized");

    s_audio_player = (audio_player_t *)audio_calloc(1, sizeof(audio_player_t));
    AUDIO_MEM_CHECK(TAG, s_audio_player, goto _exit_open);

    s_audio_player->stream_pipeline = (player_pipeline_t *)audio_calloc(1, sizeof(player_pipeline_t));
    AUDIO_MEM_CHECK(TAG, s_audio_player->stream_pipeline, goto _exit_open);
    s_audio_player->stream_pipeline->type = STREAM_MP3;

    s_audio_player->tone_pipeline   = (player_pipeline_t *)audio_calloc(1, sizeof(player_pipeline_t));
    AUDIO_MEM_CHECK(TAG, s_audio_player->tone_pipeline, goto _exit_open);
    s_audio_player->tone_pipeline->type = TONE;

    ESP_LOGI(TAG, "Initialize board peripherals");
    esp_periph_config_t periph_cfg = DEFAULT_ESP_PERIPH_SET_CONFIG();
    s_audio_player->set = esp_periph_set_init(&periph_cfg);
    AUDIO_MEM_CHECK(TAG, s_audio_player->set, goto _exit_open);

    periph_spiffs_cfg_t spiffs_cfg = {
        .root = "/spiffs",
        .partition_label = "spiffs_data",
        .max_files = 10,
        .format_if_mount_failed = true};
    s_audio_player->spiffs_handle = periph_spiffs_init(&spiffs_cfg);

    ret = esp_periph_start(s_audio_player->set, s_audio_player->spiffs_handle);
#if defined(DEBUG_VOICE_REC)
    periph_sdcard_cfg_t sdcard_cfg = {
        .root = "/sdcard",
        .mode = SD_MODE_1_LINE,
        .card_detect_pin = -1,
    };
    ESP_LOGI(TAG, "Initialize SD card peripherals");
    esp_periph_handle_t sdcard_handle = periph_sdcard_init(&sdcard_cfg);

    ret = esp_periph_start(s_audio_player->set, sdcard_handle);

    while (!periph_sdcard_is_mounted(sdcard_handle)) {
        vTaskDelay(500 / portTICK_PERIOD_MS);
    }

    ESP_LOGI(TAG, "SD card is mounted");
#endif
    // Wait until spiffs is mounted
    while (!periph_spiffs_is_mounted(s_audio_player->spiffs_handle)) {
        vTaskDelay(500 / portTICK_PERIOD_MS);
    }
    // SD card is mounted, now we can create audio board
    ESP_LOGI(TAG, "spiffs_init done");

    ret =   _audio_pipeline_init(s_audio_player->stream_pipeline);
    ret |=  _audio_pipeline_init(s_audio_player->tone_pipeline);    
    if (ret != ESP_OK)
        ESP_LOGE(TAG, "audio_pipeline init failed");

    audio_event_iface_cfg_t evt_cfg = AUDIO_EVENT_IFACE_DEFAULT_CFG();
    s_audio_player->evt_iface = audio_event_iface_init(&evt_cfg);
    AUDIO_MEM_CHECK(TAG, s_audio_player->evt_iface, goto _exit_open);
    ret = audio_pipeline_set_listener(s_audio_player->tone_pipeline->pipeline, s_audio_player->evt_iface);
    ret = audio_pipeline_set_listener(s_audio_player->stream_pipeline->pipeline, s_audio_player->evt_iface);
    AUDIO_RET_ON_FALSE(TAG, ret, goto _exit_open , "audio_pipeline_set_listener failed");
    audio_event_iface_set_listener(esp_periph_set_get_event_iface(s_audio_player->set),  s_audio_player->evt_iface);
    ESP_LOGI(TAG, "Set listener Successed.\n");

    audio_thread_create(NULL, "audio_player_state_task", audio_player_state_task, 
            (void *)s_audio_player, 5 * 1024, 15, true, 1);

    s_audio_player->stream_pipeline->player_pipeline_run  = _player_pipeline_run;
    s_audio_player->stream_pipeline->player_pipeline_stop = _player_pipeline_stop;
    s_audio_player->stream_pipeline->player_pipeline_write = _write_data_to_pip;

    s_audio_player->tone_pipeline->player_pipeline_run  = _player_pipeline_run;
    s_audio_player->tone_pipeline->player_pipeline_stop = _player_pipeline_stop;
    s_audio_player->tone_pipeline->player_pipeline_write = NULL;

    s_audio_player->get_volume = _player_audio_hal_get_volume;
    s_audio_player->set_volume = _player_audio_hal_set_volume;

    s_audio_player->is_inited = true;
    return s_audio_player;
_exit_open:
    audio_pipe_safe_free(s_audio_player->evt_iface, audio_event_iface_destroy);
    audio_pipe_safe_free(s_audio_player, audio_free);
    audio_pipe_safe_free(s_audio_player->stream_pipeline, audio_free);
    audio_pipe_safe_free(s_audio_player->tone_pipeline, audio_free);
    audio_pipe_safe_free(s_audio_player->board_handle, audio_free);
    return NULL;
}









































