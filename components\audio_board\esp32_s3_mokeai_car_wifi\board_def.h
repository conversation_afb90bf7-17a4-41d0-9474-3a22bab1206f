

#ifndef _AUDIO_BOARD_DEFINITION_H_
#define _AUDIO_BOARD_DEFINITION_H_

#include "driver/gpio.h"
#ifdef __cplusplus
extern "C" {
#endif

/**
  * @brief The step motor Function Definition
  * 
 */
#define DRV8834_EN_PIN         GPIO_NUM_5
#define DRV8834_STEP_PIN       GPIO_NUM_7
#define DRV8834_DIR_PIN        GPIO_NUM_6

/**
  * @brief The power Function Definition
  * 
 */
#define POWER_CHARGER_PIN          GPIO_NUM_46
#define POWER_BSTBY_PIN            GPIO_NUM_45
#define POWER_HOLD_PIN              GPIO_NUM_17
#define BATTERY_READ_VOL_CHANEL     ADC_CHANNEL_2

/**
 * @brief The LED Function Definition
 * 
 */
#define BUILTIN_LED_NUM            (12)
#define BUILTIN_LED_PIN            GPIO_NUM_15

 /**
  * @brief The input device Function Definition
  * 
  */
 #define TOUCH_BUTTON_PIN         GPIO_NUM_4
 #define BOOT_BUTTON_PIN          GPIO_NUM_16


/**
 * @brief The LCD Function Definition
 */
#define LCD_CS_PIN            (GPIO_NUM_44)
#define LCD_PCLK_PIN          (GPIO_NUM_40)

#define LCD_DATA0_PIN         (GPIO_NUM_42)
#define LCD_DATA1_PIN         (GPIO_NUM_41)
#define LCD_DATA2_PIN         (GPIO_NUM_48)
#define LCD_DATA3_PIN         (GPIO_NUM_39)

#define LCD_TOUCH_INT_PIN      (GPIO_NUM_47)
#define LCD_TOUCH_I2C_SDA_PIN  (GPIO_NUM_1)
#define LCD_TOUCH_I2C_SCL_PIN  (GPIO_NUM_2)
#define LCD_RST_PIN            (GPIO_NUM_NC)
#define LCD_BACKLIGHT_PIN      (GPIO_NUM_NC)



#define LCD_H_RES              (240)
#define LCD_V_RES              (135)
#define LCD_PIXEL_CLOCK_HZ     (80 * 1000 * 1000)
#define LCD_SPI_NUM            (SPI3_HOST)
#define LCD_COLOR_SPACE         (ESP_LCD_COLOR_SPACE_RGB)
#define LCD_BITS_PER_PIXEL      (16)
#define LCD_H_OFFSET              (40)
#define LCD_V_OFFSET              (52)
#define LCD_BIGENDIAN           (1)
#define LCD_DISPLAY_BACKLIGHT_OUTPUT_INVERT   false
#define LCD_MAX_ANIMATION_FRAMES       10


 


  /**
  * @brief SDCARD Function Definition
  *        PMOD2 for one line sdcard
  */
 #define FUNC_SDCARD_EN             (1)
 #define SDCARD_OPEN_FILE_NUM_MAX    5
 #define SDCARD_INTR_GPIO            -1
 #define SDCARD_PWR_CTRL             -1
 #define ESP_SD_PIN_CLK              GPIO_NUM_47
 #define ESP_SD_PIN_CMD              GPIO_NUM_48
 #define ESP_SD_PIN_D0               GPIO_NUM_21
 #define ESP_SD_PIN_D1               -1
 #define ESP_SD_PIN_D2               -1
 #define ESP_SD_PIN_D3               -1
 #define ESP_SD_PIN_D4               -1
 #define ESP_SD_PIN_D5               -1
 #define ESP_SD_PIN_D6               -1
 #define ESP_SD_PIN_D7               -1
 #define ESP_SD_PIN_CD               -1
 #define ESP_SD_PIN_WP               -1




 #define ES7210_MIC_SELECT         (ES7210_INPUT_MIC1 | ES7210_INPUT_MIC2 | ES7210_INPUT_MIC3)
 
 /**
  * @brief Audio Codec Chip Function Definition
  */
 #define FUNC_AUDIO_CODEC_EN         (1)
 #define PA_ENABLE_GPIO              GPIO_NUM_8
 #define HEADPHONE_DETECT            -1
 #define CODEC_ADC_I2S_PORT          (0)
 #define CODEC_ADC_BITS_PER_SAMPLE   I2S_BITS_PER_SAMPLE_32BIT
 #define CODEC_ADC_SAMPLE_RATE       (48000)
 #define RECORD_HARDWARE_AEC         (true)
 #define BOARD_PA_GAIN               (0) /* Power amplifier gain defined by board (dB) */
 
 extern audio_hal_func_t AUDIO_CODEC_ES8156_DEFAULT_HANDLE;
 extern audio_hal_func_t AUDIO_CODEC_ES7210_DEFAULT_HANDLE;
 #define AUDIO_CODEC_DEFAULT_CONFIG(){                   \
         .adc_input  = AUDIO_HAL_ADC_INPUT_LINE1,        \
         .dac_output = AUDIO_HAL_DAC_OUTPUT_ALL,         \
         .codec_mode = AUDIO_HAL_CODEC_MODE_BOTH,        \
         .i2s_iface = {                                  \
             .mode = AUDIO_HAL_MODE_SLAVE,               \
             .fmt = AUDIO_HAL_I2S_NORMAL,                \
             .samples = AUDIO_HAL_48K_SAMPLES,           \
             .bits = AUDIO_HAL_BIT_LENGTH_16BITS,        \
         },                                              \
 };
 
#ifdef __cplusplus
}
#endif
#endif