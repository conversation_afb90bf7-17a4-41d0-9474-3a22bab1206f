#ifndef _LED_H_
#define _LED_H_

#include <cstdint>

struct StripColor {
    uint8_t red = 0, green = 0, blue = 0;
};
class Led {
public:
    virtual ~Led() = default;
    // Set the led state based on the device state
    virtual void OnStateChanged() = 0;

    virtual void SetBreathe(StripColor low, StripColor high, int interal_ms) = 0;

    virtual void SetStaticColor(StripColor color) = 0;
    virtual void SetBlink(StripColor color, int interval_ms) = 0;
    virtual void SetFlowEffect(StripColor color, int interval_ms) = 0;
    
};


class NoLed : public Led {
public:
    virtual void OnStateChanged() override {}
    virtual void SetBreathe(StripColor low, StripColor high, int interal_ms) override {};
    
    virtual void SetStaticColor(StripColor color) override {};
    virtual void SetBlink(StripColor color, int interval_ms) override {};
    virtual void SetFlowEffect(StripColor color, int interval_ms) override {};
};

#endif // _LED_H_
