From 9ce9dcf0a7c2aed09372ddac3f40a15ad29669c8 Mon Sep 17 00:00:00 2001
From: "huimin.li" <<EMAIL>>
Date: Fri, 1 Aug 2025 17:51:18 +0800
Subject: [PATCH] Feat: overried VAD_CRL

---
 components/audio_recorder/audio_recorder.c    |  5 +++++
 .../include/recorder_sr_iface.h               |  4 ++++
 components/audio_recorder/recorder_sr.c       | 20 +++++++++++++++++++
 3 files changed, 29 insertions(+)

diff --git a/components/audio_recorder/audio_recorder.c b/components/audio_recorder/audio_recorder.c
index c3815e0d..2df15206 100644
--- a/components/audio_recorder/audio_recorder.c
+++ b/components/audio_recorder/audio_recorder.c
@@ -422,9 +422,14 @@ static void audio_recorder_task(void *parameters)
                 running = false;
                 break;
             case RECORDER_CMD_VAD_CTRL:
+            #if 0
                 audio_recorder_reset(recorder);
                 recorder->vad_check = (bool)msg.data;
                 ESP_LOGI(TAG, "recorder vad %d", recorder->vad_check);
+            #endif
+                if (recorder->sr_handle && recorder->sr_iface) {
+                    recorder->sr_iface->vad_enable(recorder->sr_handle, (bool)msg.data);
+                }
                 break;
             case RECORDER_CMD_WWE_CTRL:
                 audio_recorder_reset(recorder);
diff --git a/components/audio_recorder/include/recorder_sr_iface.h b/components/audio_recorder/include/recorder_sr_iface.h
index b2e05d73..a61948dd 100644
--- a/components/audio_recorder/include/recorder_sr_iface.h
+++ b/components/audio_recorder/include/recorder_sr_iface.h
@@ -154,6 +154,10 @@ typedef struct {
      *          ESP_ERR_INVALID_ARG
      */
     esp_err_t (*mn_enable)(void *handle, bool enable);
+
+
+    esp_err_t (*vad_enable)(void *handle, bool enable);
+
 } recorder_sr_iface_t;
 
 #ifdef __cplusplus
diff --git a/components/audio_recorder/recorder_sr.c b/components/audio_recorder/recorder_sr.c
index 6d51e2d3..d6c50d7a 100644
--- a/components/audio_recorder/recorder_sr.c
+++ b/components/audio_recorder/recorder_sr.c
@@ -416,6 +416,25 @@ static esp_err_t recorder_sr_wwe_enable(void *handle, bool enable)
     return ret == 1 ? ESP_OK : ESP_FAIL;
 }
 
+
+
+static esp_err_t recorder_sr_vad_enable(void *handle, bool enable)
+{
+    AUDIO_CHECK(TAG, handle, return ESP_ERR_INVALID_ARG, "Handle is NULL");
+    recorder_sr_t *recorder_sr = (recorder_sr_t *)handle;
+    int ret = 0;
+
+    if (enable) {
+        
+        ret = recorder_sr->afe_handle->enable_vad(recorder_sr->afe_data);
+    } else 
+    {
+       ret = recorder_sr->afe_handle->disable_vad(recorder_sr->afe_data);
+    }
+    ESP_LOGI(TAG, "VAD enable: %d", ret);
+    return ret == 1 ? ESP_OK : ESP_FAIL;
+}
+
 static esp_err_t recorder_sr_mn_enable(void *handle, bool enable)
 {
 #if !defined(CONFIG_SR_MN_CN_NONE) || !defined(CONFIG_SR_MN_EN_NONE)
@@ -455,6 +474,7 @@ static recorder_sr_iface_t recorder_sr_iface = {
     .set_mn_monitor = recorder_sr_set_mn_monitor,
     .wwe_enable = recorder_sr_wwe_enable,
     .mn_enable = recorder_sr_mn_enable,
+    .vad_enable = recorder_sr_vad_enable,
 };
 
 static void recorder_sr_clear(void *handle)
-- 
2.26.0.windows.1

