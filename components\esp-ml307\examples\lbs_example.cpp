#include "ml307_at_modem.h"
#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

static const char* TAG = "LBS_Example";

void lbs_example_task(void* pvParameters) {
    // 创建ML307调制解调器实例
    Ml307AtModem modem(GPIO_NUM_17, GPIO_NUM_18);  // TX, RX引脚
    
    // 启用调试输出
    modem.SetDebug(true);
    
    // 检测并设置波特率
    if (!modem.SetBaudRate(115200)) {
        ESP_LOGE(TAG, "Failed to set baud rate");
        vTaskDelete(NULL);
        return;
    }
    
    // 等待网络就绪
    int network_status = modem.WaitForNetworkReady();
    if (network_status != 0) {
        ESP_LOGE(TAG, "Network not ready, status: %d", network_status);
        vTaskDelete(NULL);
        return;
    }
    
    ESP_LOGI(TAG, "Network ready, starting LBS configuration...");
    
    // 示例1: 配置高德智能硬件定位1.0
    ESP_LOGI(TAG, "=== 配置高德智能硬件定位1.0 ===");
    
    // 配置LBS定位平台为高德智能硬件定位1.0
    if (modem.ConfigureLbsMethod(LbsMethod::AMAP_V1)) {
        ESP_LOGI(TAG, "LBS method configured successfully");
    } else {
        ESP_LOGE(TAG, "Failed to configure LBS method");
    }
    
    // 配置API Key (需要替换为实际的API Key)
    if (modem.ConfigureLbsApiKey("your_amap_api_key_here")) {
        ESP_LOGI(TAG, "API key configured successfully");
    } else {
        ESP_LOGE(TAG, "Failed to configure API key");
    }
    
    // 可选：配置数字签名（如果平台开启了数字签名）
    // modem.ConfigureLbsSignKey("your_sign_key_here");
    // modem.ConfigureLbsSignEnable(true);
    
    // 配置定位精度（经纬度小数位数）
    if (modem.ConfigureLbsPrecision(8)) {
        ESP_LOGI(TAG, "Precision configured successfully");
    }
    
    // 启用位置描述信息输出
    if (modem.ConfigureLbsFormat(true)) {
        ESP_LOGI(TAG, "Format configured successfully");
    }
    
    // 启用邻区信息参与定位（提高精度但增加定位时间）
    if (modem.ConfigureLbsNearBtsEnable(true)) {
        ESP_LOGI(TAG, "Near BTS enabled successfully");
    }
    
    // 获取位置信息
    ESP_LOGI(TAG, "Getting location information...");
    LbsLocationInfo location = modem.GetLocationInfo();
    
    if (location.is_valid()) {
        ESP_LOGI(TAG, "Location obtained successfully:");
        ESP_LOGI(TAG, "  Status: %d", location.status);
        ESP_LOGI(TAG, "  Longitude: %.8f", location.longitude);
        ESP_LOGI(TAG, "  Latitude: %.8f", location.latitude);
        ESP_LOGI(TAG, "  Radius: %d meters", location.radius);
        if (!location.description.empty()) {
            ESP_LOGI(TAG, "  Description: %s", location.description.c_str());
        }
    } else {
        ESP_LOGE(TAG, "Failed to get location, status: %d", location.status);
        
        // 打印错误码含义
        switch (location.status) {
            case 120: ESP_LOGE(TAG, "Error: Request timeout"); break;
            case 121: ESP_LOGE(TAG, "Error: Invalid or expired API key"); break;
            case 122: ESP_LOGE(TAG, "Error: Invalid request parameters"); break;
            case 123: ESP_LOGE(TAG, "Error: Daily quota exceeded"); break;
            case 124: ESP_LOGE(TAG, "Error: QPS limit exceeded"); break;
            case 125: ESP_LOGE(TAG, "Error: Total quota exceeded"); break;
            case 126: ESP_LOGE(TAG, "Error: Unknown error"); break;
            case 104: ESP_LOGE(TAG, "Error: Parameter error"); break;
            case 105: ESP_LOGE(TAG, "Error: LBS busy"); break;
            default: ESP_LOGE(TAG, "Error: Unknown status code %d", location.status); break;
        }
    }
    
    // 示例2: 配置OneOs定位服务
    ESP_LOGI(TAG, "=== 配置OneOs定位服务 ===");
    
    // 配置LBS定位平台为OneOs定位服务
    if (modem.ConfigureLbsMethod(LbsMethod::ONEOS)) {
        ESP_LOGI(TAG, "OneOs method configured successfully");
    }
    
    // OneOs服务已内置PID，通常不需要配置
    // 如果需要自定义PID，可以使用：
    // modem.ConfigureLbsPid("your_oneos_pid_here");
    
    // 再次获取位置信息
    ESP_LOGI(TAG, "Getting location with OneOs service...");
    location = modem.GetLocationInfo();
    
    if (location.is_valid()) {
        ESP_LOGI(TAG, "OneOs location obtained successfully:");
        ESP_LOGI(TAG, "  Status: %d", location.status);
        ESP_LOGI(TAG, "  Longitude: %.8f", location.longitude);
        ESP_LOGI(TAG, "  Latitude: %.8f", location.latitude);
        // 注意：OneOs服务不支持radius和description
    } else {
        ESP_LOGE(TAG, "Failed to get OneOs location, status: %d", location.status);
    }
    
    // 查询当前配置
    ESP_LOGI(TAG, "=== 查询当前LBS配置 ===");
    LbsMethod current_method = modem.GetLbsMethod();
    ESP_LOGI(TAG, "Current LBS method: %d", static_cast<int>(current_method));
    ESP_LOGI(TAG, "Current precision: %d", modem.GetLbsPrecision());
    ESP_LOGI(TAG, "Format enabled: %s", modem.GetLbsFormat() ? "Yes" : "No");
    ESP_LOGI(TAG, "Near BTS enabled: %s", modem.GetLbsNearBtsEnable() ? "Yes" : "No");
    
    ESP_LOGI(TAG, "LBS example completed");
    vTaskDelete(NULL);
}

extern "C" void app_main() {
    // 创建LBS示例任务
    xTaskCreate(lbs_example_task, "lbs_example", 8192, NULL, 5, NULL);
}
