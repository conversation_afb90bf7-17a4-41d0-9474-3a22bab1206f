/*
 * ESPRESSIF MIT License
 *
 * Copyright (c) 2025 <ESPRESSIF SYSTEMS (SHANGHAI) CO., LTD>
 *
 * Permission is hereby granted for use on all ESPRESSIF SYSTEMS products, in which case,
 * it is free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the Software is furnished
 * to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or
 * substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 */


#pragma once

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include "audio_pipeline.h"
#include "audio_element.h"
#include "board.h"
#include "esp_peripherals.h"
#ifdef __cplusplus
extern "C" {
#endif

typedef enum 
{
    TONE = 0,
    STREAM_MP3,
} audio_pipeline_type_t;

/**
 * @brief Enumeration of player pipeline states
 */
typedef enum {
    PIPE_STATE_IDLE,     /**< The pipeline is idle and not processing any audio */
    PIPE_STATE_RUNNING,  /**< The pipeline is actively processing and playing audio */
    PIPE_STATE_INITED, 
} pipe_player_state_e;

typedef struct player_pipeline
{
    audio_pipeline_handle_t pipeline;
    audio_element_handle_t  raw;
    audio_element_handle_t  decoder;
    audio_element_handle_t  i2s;
    audio_element_handle_t  rsp;
    audio_element_handle_t  spiffs;
    pipe_player_state_e     state;
    audio_pipeline_type_t   type;
    
    esp_err_t (*player_pipeline_open)(struct player_pipeline *pip);
    esp_err_t (*player_pipeline_run)(struct player_pipeline *pip, const char *uri);
    esp_err_t (*player_pipeline_stop)(struct player_pipeline *pip);
    esp_err_t (*player_pipeline_close)(struct player_pipeline *pip);
    size_t (*player_pipeline_write)(void *data, size_t len);
    uint64_t audio_frame_cnt;
    bool is_playing;
}player_pipeline_t;

typedef struct audio_player
{
    player_pipeline_t *stream_pipeline;
    player_pipeline_t *tone_pipeline;
    audio_hal_handle_t     audio_hal;
    int                    volume;
    audio_board_handle_t  board_handle;
    esp_periph_set_handle_t set;
    audio_event_iface_handle_t evt_iface;
    esp_periph_handle_t spiffs_handle;
    esp_err_t (*set_volume)(int volume);
    esp_err_t (*get_volume)(int *volume);
    bool running;
    bool is_inited;
}audio_player_t;

/**
 * @brief Create audio player
 *
 * @return
 *      - audio player handle
 */
audio_player_t * create_audio_player(void);

/**
 * @brief Flush audio pipeline with silence data to clear residual audio
 *
 * This function writes silence data to the audio pipeline to push out
 * any residual audio data that might remain in the pipeline buffers.
 * Useful when audio stream is interrupted and you want to prevent
 * old audio from playing when the stream resumes.
 *
 * @return
 *      - ESP_OK on success
 *      - ESP_FAIL on failure
 */
esp_err_t audio_player_flush_pipeline_silence(void);

#ifdef __cplusplus
}
#endif
