#ifndef _BDVS_BUFFER_H_
#define _BDVS_BUFFER_H_

#include <stdint.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"
#include "esp_log.h"

/**
 * @brief Function to free individual elements in buffer
 */
typedef void (*bdvs_element_free_fn)(void*);

typedef struct {
    void *buffer;
    uint32_t head;
    uint32_t tail;
    uint32_t size;
    size_t element_size;
    bdvs_element_free_fn free_fn;
    SemaphoreHandle_t mutex;
    bool is_external;
    void *user_data;  // Pointer for user-defined data
} bdvs_buffer_t;

// User data management APIs
void bdvs_buffer_set_user_data(bdvs_buffer_t *buf, void *user_data);
void* bdvs_buffer_get_user_data(bdvs_buffer_t *buf);

/**
 * @brief Create a new circular buffer
 * @param size Number of elements the buffer can hold
 * @param element_size Size of each element in bytes
 * @param free_fn Optional function to free individual elements when buffer is destroyed
 * @param external_buf Optional external buffer to use instead of allocating memory
 * @return Pointer to new buffer, NULL on failure
 * @note If external_buf is provided, it must remain valid for the buffer's lifetime
 */
bdvs_buffer_t* bdvs_buffer_create(uint32_t size, size_t element_size, 
                                bdvs_element_free_fn free_fn, void *external_buf);

/**
 * @brief Destroy a buffer and free all resources
 * @param buf Buffer to destroy
 * @note If free_fn was provided, it will be called for each remaining element
 */
void bdvs_buffer_destroy(bdvs_buffer_t *buf);

/**
 * @brief Write data to the buffer
 * @param buf Buffer to write to
 * @param data Data to write
 * @param len Number of elements to write
 * @return true on success, false if buffer is full or invalid parameters
 * @note This operation is thread-safe
 */
bool bdvs_buffer_write(bdvs_buffer_t *buf, const uint8_t *data, uint32_t len);

/**
 * @brief Read data from the buffer
 * @param buf Buffer to read from
 * @param data Buffer to store read data
 * @param len Maximum number of elements to read
 * @return Number of elements actually read
 * @note This operation is thread-safe
 */
uint32_t bdvs_buffer_read(bdvs_buffer_t *buf, uint8_t *data, uint32_t len);

/**
 * @brief Get number of available elements in buffer
 * @param buf Buffer to check
 * @return Number of available elements
 */
uint32_t bdvs_buffer_available(bdvs_buffer_t *buf);

/**
 * @brief Get free space in buffer
 * @param buf Buffer to check
 * @return Number of free elements
 */
uint32_t bdvs_buffer_free(bdvs_buffer_t *buf);

/**
 * @brief Clear all data from buffer
 * @param buf Buffer to clear
 * @note If free_fn was provided, it will be called for each element
 */
void bdvs_buffer_clear(bdvs_buffer_t *buf);

/**
 * @brief Push a single element to buffer (alias for write)
 * @param buf Buffer to push to
 * @param data Element to push
 * @param size Size of element (must match buffer's element_size)
 * @return 0 on success, -1 on failure
 */
int bdvs_buffer_push(bdvs_buffer_t *buf, const void *data, size_t size);

/**
 * @brief Pop a single element from buffer (alias for read)
 * @param buf Buffer to pop from
 * @param data Buffer to store popped element
 * @param size Size of element (must match buffer's element_size)
 * @return 0 on success, -1 on failure
 */
int bdvs_buffer_pop(bdvs_buffer_t *buf, void *data, size_t size);

/**
 * @brief Check if buffer is empty
 * @param buf Buffer to check
 * @return true if empty, false if not empty or invalid buffer
 */
bool bdvs_buffer_empty(bdvs_buffer_t *buf);

/**
 * @brief Check if the current element is the last element in the buffer.
 * 
 * This function determines whether the specified `current_element` is the last element in the given buffer.
 * It is useful for iterating through a buffer and identifying the end condition.
 * 
 * @param buf Pointer to the buffer structure to check. If `buf` is `NULL` or invalid, the function returns `false`.
 * @param current_element Pointer to the element being checked. If `current_element` is `NULL` or invalid, the function returns `false`.
 * 
 * @return true if `current_element` is the last element in the buffer, false otherwise or if the buffer is invalid.
 * 
 * @note This function assumes that the buffer and its elements are properly initialized and managed.
 *       It does not check for buffer overflow or underflow conditions.
 */
bool bdvs_buffer_is_last_element(bdvs_buffer_t *buf, void *current_element);

/**
 * @example
 * // Example usage of bdvs_buffer
 * 
 // Create a buffer that can hold 100 integers
 * bdvs_buffer_t *buf = bdvs_buffer_create(100, sizeof(int), NULL, NULL);
 * 
 * // Write data to buffer
 * int data[10] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
 * bdvs_buffer_write(buf, (uint8_t*)data, 10);
 * 
 * // Read data from buffer
 * int read_data[10];
 * uint32_t read_count = bdvs_buffer_read(buf, (uint8_t*)read_data, 10);
 * 
 * // Check available data
 * uint32_t available = bdvs_buffer_available(buf);
 * 
 * // Push single element
 * int push_data = 42;
 * bdvs_buffer_push(buf, &push_data, sizeof(int));
 * 
 * // Pop single element
 * int pop_data;
 * bdvs_buffer_pop(buf, &pop_data, sizeof(int));
 * 
 * // Clear buffer
 * bdvs_buffer_clear(buf);
 * 
 * // Destroy buffer when done
 * bdvs_buffer_destroy(buf);
 */

#endif
