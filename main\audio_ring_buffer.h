#pragma once
#include <vector>
#include <memory>
#include <mutex>
#include <condition_variable>
#include <cstdint>
#include <cstring>
#include "esp_heap_caps.h"

#if 1
struct SpiramDeleter {
    void operator()(uint8_t* p) const;
};

struct AudioBufferItem {
    std::unique_ptr<uint8_t[], SpiramDeleter> data;
    size_t size;
};
#else

struct AudioBufferItem {
    std::unique_ptr<uint8_t[]> data;
    size_t size;
};
#endif

class AudioRingBuffer {
public:
    explicit AudioRingBuffer(size_t item_count, size_t item_size);
    
    bool Push(const void* data, size_t size);
    bool Pop(AudioBufferItem& output);
    
    size_t Size() const;
    bool Empty() const;
    bool Full() const;
    void Clear();

private:
    std::vector<AudioBufferItem> buffer_;
    std::mutex mutex_;
    size_t capacity_;
    size_t item_size_;
    size_t head_ = 0;
    size_t tail_ = 0;
    bool full_ = false;
};