
# 基础配置
set(COMPONENT_ADD_INCLUDEDIRS ./include)
set(COMPONENT_REQUIRES 
    78__esp-wifi-connect
    esp-ml307 
    espressif__esp_lvgl_port
    espressif__button 
    espressif__led_strip
    esp_peripherals
    audio_sal 
    audio_hal
    spi_flash  
    esp_dispatcher 
    display_service 
    driver 
    battery_service 
    esp_adc
    app_update 
    lvgl__lvgl
    device_status_manager
    esp_lcd_icna3306
    espressif__esp_lcd_touch_cst816s
    78__xiaozhi-fonts
)

# 公共文件配置
set(COMMON_SRCS
    ./common/board/board.cc
    ./common/board/wifi_board.cc
    ./common/board/ml307_board.cc
    ./common/led/gpio_led.cc
    ./common/led/circular_strip.cc
    ./common/led/single_led.cc
    ./common/motor/drv8834.cc
    ./common/button.cc
    ./common/system_info.cc
    ./common/settings.cc
    ./common/backlight.cc
    ./common/system_reset.cc
    ./common/touch_button.cc
    ./common/power_save_timer.cc
)

set(COMMON_INCLUDES
    ./common
    ./common/led
    ./common/board
    ./common/motor
    ../../main
)

# 特定板型配置
macro(configure_board BOARD_TYPE SOURCES)
    list(APPEND COMPONENT_ADD_INCLUDEDIRS 
        ${COMMON_INCLUDES}
        ./${BOARD_TYPE}
    )
    
    set(COMPONENT_SRCS
        ${COMMON_SRCS}
        ./${BOARD_TYPE}/${BOARD_TYPE}.cc
        ./${BOARD_TYPE}/board_pins_config.c
    )
endmacro()

# 板型选择
if(CONFIG_ESP32_S3_MOKEAI_BOARD)
    configure_board("esp32_s3_mokeai_desk" "")
elseif(CONFIG_ESP32_S3_MOKEAI_CAR_4G_BOARD)
    configure_board("esp32_s3_mokeai_car_4g" "")
    spiffs_create_partition_image(image ./esp32_s3_mokeai_car_4g/emoji/ FLASH_IN_PROJECT)
elseif(CONFIG_ESP32_S3_MOKEAI_CAR_WIFI_BOARD)
    configure_board("esp32_s3_mokeai_car_wifi" "")
    spiffs_create_partition_image(image ./esp32_s3_mokeai_car_wifi/emoji/ FLASH_IN_PROJECT)
endif()

register_component()
