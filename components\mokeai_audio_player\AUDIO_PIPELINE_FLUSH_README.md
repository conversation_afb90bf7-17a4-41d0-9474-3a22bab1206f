# 音频管道静音冲刷功能

## 问题描述

在 AudioPlayerStreamPIPE 中，当 raw stream 数据流突然断开时，管道中会残留一些音频数据。当下次数据流恢复时，这些残留的旧数据会被播放出来，导致音频播放异常。

## 解决方案

实现了一个静音数据冲刷机制，通过向管道中填充静音数据来将旧的残留数据"顶出"管道，确保下次播放时不会有旧数据干扰。

## 实现细节

### 1. 静音数据定义
```c
#define SILENCE_FRAME_SIZE 1600  // 16-bit, 16kHz, mono, 50ms → 1600 bytes
static uint8_t silence_frame[SILENCE_FRAME_SIZE] = {0};
```

### 2. 核心冲刷函数
```c
static esp_err_t _flush_pipeline_with_silence(struct player_pipeline *player_pipeline)
```
- 向管道写入5帧静音数据（约250ms）
- 每帧之间有10ms延时，确保管道有时间处理
- 最后等待100ms让静音数据通过整个管道

### 3. 自动冲刷
在 `_player_pipeline_stop()` 函数中，停止 STREAM_MP3 类型的管道时会自动进行静音冲刷：
```c
// 在停止管道之前，先用静音数据冲刷管道，清除残留数据
ESP_LOGI(TAG, "Flushing pipeline with silence before stop");
_flush_pipeline_with_silence(player_pipeline);
```

### 4. 手动冲刷接口
提供了公共接口供外部调用：
```c
esp_err_t audio_player_flush_pipeline_silence(void);
```

## 使用方法

### 自动使用
停止音频播放时会自动进行静音冲刷，无需额外操作：
```c
player->stream_pipeline->player_pipeline_stop(player->stream_pipeline);
```

### 手动使用
在音频流中断但不停止播放器的情况下：
```c
// 检测到音频流中断
if (audio_stream_interrupted) {
    // 手动冲刷管道
    esp_err_t ret = audio_player_flush_pipeline_silence();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Pipeline flushed successfully");
    }
}
```

## 适用场景

1. **网络音频流中断**：网络不稳定导致音频数据流中断时
2. **音频源切换**：从一个音频源切换到另一个音频源时
3. **音频质量问题**：发现音频播放有杂音或异常时
4. **长时间暂停后恢复**：音频暂停很长时间后恢复播放时

## 技术参数

- **静音帧大小**：1600字节（16-bit, 16kHz, mono, 50ms）
- **冲刷帧数**：5帧
- **总冲刷时长**：约250ms
- **处理延时**：每帧10ms + 最终100ms = 150ms处理时间

## 注意事项

1. 只有在管道处于 `PIPE_STATE_RUNNING` 状态时才能进行手动冲刷
2. 冲刷过程会产生约250ms的静音，这是正常现象
3. 自动冲刷在停止管道时进行，通常不需要手动调用
4. 如果管道未初始化或已停止，手动冲刷会返回失败

## 日志输出

正常冲刷时会看到以下日志：
```
I (xxx) AUDIO_PLAYER: Flushing pipeline with 5 silence frames (1600 bytes each)
I (xxx) AUDIO_PLAYER: Pipeline flushed with silence data
```

## 性能影响

- 内存占用：增加1600字节静态缓冲区
- CPU占用：冲刷过程中有轻微的CPU开销
- 延时：停止播放时增加约150ms的处理时间
