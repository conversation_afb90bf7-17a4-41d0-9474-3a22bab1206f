From cde538f43f522516b4d1f5c478c4c383de3b82c3 Mon Sep 17 00:00:00 2001
From: "huimin.li" <<EMAIL>>
Date: Mon, 23 Jun 2025 16:39:18 +0800
Subject: [PATCH] Fixbug: When vad is triggered, the frame loss problem

---
 components/audio_recorder/audio_recorder.c | 2 ++
 1 file changed, 2 insertions(+)

diff --git a/components/audio_recorder/audio_recorder.c b/components/audio_recorder/audio_recorder.c
index 0d30ba4c..d8930429 100644
--- a/components/audio_recorder/audio_recorder.c
+++ b/components/audio_recorder/audio_recorder.c
@@ -688,10 +688,12 @@ int audio_recorder_data_read(audio_rec_handle_t handle, void *buffer, int length
     audio_recorder_t *recorder = (audio_recorder_t *)handle;
     recorder_subproc_iface_t *subproc = NULL;
     int ret = 0;
+    #if 0
     if (recorder->state != RECORDER_ST_SPEECHING && recorder->state != RECORDER_ST_WAIT_FOR_SILENCE) {
         ESP_LOGW(TAG, "Not in speeching, return 0");
         return 0;
     }
+    #endif
     if (recorder->encoder_handle) {
         subproc = &recorder->encoder_iface->base;
         ret = subproc->fetch(recorder->encoder_handle, buffer, length, ticks);
-- 
2.26.0.windows.1

