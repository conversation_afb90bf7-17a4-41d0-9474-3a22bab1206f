/*
 * ESPRESSIF MIT License
 *
 * Copyright (c) 2022 <ESPRESSIF SYSTEMS (SHANGHAI) CO., LTD>
 *
 * Permission is hereby granted for use on all ESPRESSIF SYSTEMS products, in which case,
 * it is free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the Software is furnished
 * to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or
 * substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 */

static const char *TAG = "AUDIO_BOARD";

#include "esp_log.h"
#include <wifi_board.h>
#include <button.h>
#include "esp_log.h"
#include "board_def.h"
#include "audio_hal.h"
#include "led.h"
#include "circular_strip.h"
#include "system_reset.h"
#include "touch_button.h"
#include "power_manager.h"
#include "device_status_manager.h"

#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_vendor.h"
#include "esp_lcd_panel_ops.h"
#include "esp_spiffs.h"
#include "backlight.h"
#include "lvgl.h"
#include "esp_lvgl_port.h"
#include "drv8834.h"
#include "power_save_timer.h"

class Esp32S3MokeAICarWifiBoard : public WifiBoard
{
private:
    Button reset_button_;
    TouchSensor touch_button_;
    SystemReset *reseter;
    PowerManager *power_manager_;
    audio_hal_handle_t codec_hal;
    audio_hal_handle_t adc_hal;
    PowerSaveTimer *power_save_timer_;
    esp_lcd_panel_io_handle_t panel_io = nullptr;
    esp_lcd_panel_handle_t panel = nullptr;
    lv_display_t *display = nullptr;
    lv_obj_t *emoji = nullptr;

public:
    enum class EmojiType
    {
        Wakeup,
        Sleeping,
        Listening,
        Thinking,
        NetworkConfiging,
        NetworkConnecting,
        NetworkErr,
        Default
    };

    void InitializeSpi()
    {
        spi_bus_config_t buscfg = {};
        buscfg.mosi_io_num = GPIO_NUM_40;
        buscfg.miso_io_num = GPIO_NUM_NC;
        buscfg.sclk_io_num = GPIO_NUM_41;
        buscfg.quadwp_io_num = GPIO_NUM_NC;
        buscfg.quadhd_io_num = GPIO_NUM_NC;
        buscfg.max_transfer_sz = LCD_H_RES * LCD_V_RES;
        ESP_ERROR_CHECK(spi_bus_initialize(LCD_SPI_NUM, &buscfg, SPI_DMA_CH_AUTO));
    }

    void InitializeSt7789Display()
    {

        ESP_LOGI(TAG, "Install panel IO");
        esp_lcd_panel_io_spi_config_t io_config = {};
        io_config.cs_gpio_num = GPIO_NUM_44;
        io_config.dc_gpio_num = GPIO_NUM_39;
        io_config.spi_mode = 0;
        io_config.pclk_hz = LCD_PIXEL_CLOCK_HZ;
        io_config.trans_queue_depth = 7;
        io_config.lcd_cmd_bits = 8;
        io_config.lcd_param_bits = 8;
        esp_lcd_new_panel_io_spi(LCD_SPI_NUM, &io_config, &panel_io);

        ESP_LOGI(TAG, "Install LCD driver");
        esp_lcd_panel_dev_config_t panel_config = {};
        panel_config.reset_gpio_num = GPIO_NUM_NC;
        panel_config.rgb_ele_order = LCD_COLOR_SPACE;
        panel_config.bits_per_pixel = 16;

        esp_lcd_new_panel_st7789(panel_io, &panel_config, &panel);

        esp_lcd_panel_reset(panel);
        esp_lcd_panel_init(panel);
        esp_lcd_panel_set_gap(panel, LCD_H_OFFSET, LCD_V_OFFSET);
        esp_lcd_panel_invert_color(panel, true);
        esp_lcd_panel_swap_xy(panel, true);
        esp_lcd_panel_mirror(panel, false, true);
    }

    void LVGLInit(void)
    {
        ESP_LOGI(TAG, "Initialize LVGL library");
        lv_init();
        ESP_LOGI(TAG, "Initialize LVGL port");
        lvgl_port_cfg_t port_cfg = ESP_LVGL_PORT_INIT_CONFIG();
        port_cfg.task_affinity = 1; // 设置LVGL的运行核心为1
        lvgl_port_init(&port_cfg);

        esp_lcd_panel_disp_on_off(panel, true);

        /* Add LCD screen */
        ESP_LOGD(TAG, "Add LCD screen");
        const lvgl_port_display_cfg_t disp_cfg = {
            .io_handle = panel_io,
            .panel_handle = panel,
            .buffer_size = static_cast<uint32_t>(LCD_H_RES * 20),
            .double_buffer = false,
            .hres = LCD_H_RES,
            .vres = LCD_V_RES,
            .monochrome = false,
            /* Rotation values must be same as used in esp_lcd for initial settings of the screen */
            .rotation = {
                .swap_xy = true,
                .mirror_x = false,
                .mirror_y = true,
            },
            .flags = {
                .buff_dma = 1,
                .buff_spiram = 0,
#if LVGL_VERSION_MAJOR >= 9
                .swap_bytes = (LCD_BIGENDIAN ? true : false),
#endif
            }};
        display = lvgl_port_add_disp(&disp_cfg);
    }

    void LVGLMountSpiffs(void)
    {
        esp_vfs_spiffs_conf_t conf = {
            .base_path = "/image",
            .partition_label = "image",
            .max_files = 5,
            .format_if_mount_failed = false,
        };
        esp_vfs_spiffs_register(&conf);
    }

    void InitializePowerSaveTimer()
    {
        power_save_timer_ = new PowerSaveTimer(240, 60);
        power_save_timer_->OnEnterSleepMode(
            [this]()
            {
                ESP_LOGI(TAG, "Enabling sleep mode");
                auto *led = GetLed();
                auto *backlight = GetBacklight();
                struct StripColor high = {0, 0, 50};
                led->SetStaticColor(high);
                backlight->SetBrightness(1);
            });
        power_save_timer_->OnExitSleepMode(
            [this]()
            {
                ESP_LOGI(TAG, "Disabling sleep mode");
            });
        power_save_timer_->SetEnabled(true);
    }

    void InitializePowerManager()
    {
        power_manager_ = new PowerManager(POWER_CHARGER_PIN, BATTERY_READ_VOL_CHANEL);
        power_manager_->OnChargingStatusChanged([this](bool is_charging)
                                                {
            
            DeviceStateMsg_t msg = {STATE_BATT_CHARGING,  nullptr};
            auto &device_state_manager = DeviceStatusManger::GetInstance();
            

            if (is_charging) {
                device_state_manager.SetDeviceStatus(&msg);
                power_save_timer_->SetEnabled(false);
            } else {
                msg.state = STATE_BATT_DSCHARGING;
                device_state_manager.SetDeviceStatus(&msg);
                power_save_timer_->SetEnabled(true);
            } });
        power_manager_->OnBatteryLevelReport(
            [this](uint32_t level)
            {
            static uint32_t batt_level = 0;
            batt_level = level;
            DeviceStateMsg_t msg = {STATE_BATT_LEVEL_REPOART,  (void *)&batt_level};
            auto &device_state_manager = DeviceStatusManger::GetInstance();
            device_state_manager.SetDeviceStatus(&msg); });

        power_manager_->OnLowBatteryStatusChanged(
            [this](uint8_t low)
            {
                DeviceStateMsg_t msg = {STATE_BATT_LOW, nullptr};
                auto &device_state_manager = DeviceStatusManger::GetInstance();
                device_state_manager.SetDeviceStatus(&msg);
            });
    }

    void InitializeButtons()
    {
        reset_button_.OnLongPress(
            [this]()
            {
                auto &device_state_manager = DeviceStatusManger::GetInstance();
                static std::string str = "reset_button";
                ESP_LOGI(TAG, "Reset button long press");
                DeviceStateMsg_t msg = {STATE_BUTTON_LONG_PRESS, (void *)str.c_str()};
                device_state_manager.SetDeviceStatus(&msg);
                PowerOff();
            });

        reset_button_.OnDoubleClick(
            [this]()
            {
                static std::string str = "reset_button";
                ESP_LOGD(TAG, "Reset button double click press");
                DeviceStateMsg_t msg = {STATE_BUTTON_DOUBLE_CLICK, (void *)str.c_str()};
                auto &device_state_manager = DeviceStatusManger::GetInstance();
                device_state_manager.SetDeviceStatus(&msg);
                reseter->ResetNvsFlash();
                reseter->RestartInSeconds(3);
            });

        touch_button_.OnTouch(
            [this]()
            {
                static std::string str = "touch_button";
                DeviceStateMsg_t msg = {STATE_BUTTON_CLICK, (void *)str.c_str()};
                auto &device_state_manager = DeviceStatusManger::GetInstance();
                device_state_manager.SetDeviceStatus(&msg);
            });
    }

    void InitializeSystemPowerPin(void)
    {
        gpio_config_t io_conf = {
            .pin_bit_mask = 1ULL << POWER_HOLD_PIN, // 设置需要配置的 GPIO 引脚
            .mode = GPIO_MODE_OUTPUT,               // 设置为输出模式
            .pull_up_en = GPIO_PULLUP_DISABLE,      // 禁用上拉
            .pull_down_en = GPIO_PULLDOWN_DISABLE,  // 禁用下拉
            .intr_type = GPIO_INTR_DISABLE          // 禁用中断
        };
        gpio_config(&io_conf); // 应用配置
    }

    void PowerOn(void)
    {
        gpio_set_level(POWER_HOLD_PIN, 1);
    }

    void PowerOff(void)
    {
        gpio_set_level(POWER_HOLD_PIN, 0);
    }

    std::string GetBoardName(void) override
    {
        return "mokeai_car_wifi";
    }

    audio_hal_handle_t CodecInit(void)
    {
        audio_hal_codec_config_t audio_codec_cfg = AUDIO_CODEC_DEFAULT_CONFIG();
        audio_codec_cfg.i2s_iface.samples = AUDIO_HAL_16K_SAMPLES;
        audio_hal_handle_t codec_hal = audio_hal_init(&audio_codec_cfg, &AUDIO_CODEC_ES8156_DEFAULT_HANDLE);
        AUDIO_NULL_CHECK(TAG, codec_hal, return NULL);
        return codec_hal;
    }

    audio_hal_handle_t AdcInit(void)
    {
        audio_hal_codec_config_t audio_codec_cfg = AUDIO_CODEC_DEFAULT_CONFIG();

        audio_hal_handle_t adc_hal = NULL;
        adc_hal = audio_hal_init(&audio_codec_cfg, &AUDIO_CODEC_ES7210_DEFAULT_HANDLE);
        AUDIO_NULL_CHECK(TAG, adc_hal, return NULL);
        return adc_hal;
    }

    virtual Led *GetLed() override
    {
        static CircularStrip led(BUILTIN_LED_PIN, 8);
        return &led;
    }

    static void wakeup_gif_timer_callback(TimerHandle_t xTimer)
    {
        Esp32S3MokeAICarWifiBoard *board = (Esp32S3MokeAICarWifiBoard *)pvTimerGetTimerID(xTimer);
        if (board)
        {
            board->SetDisEmoji(EmojiType::Default);
        }
    }

    Esp32S3MokeAICarWifiBoard() : WifiBoard(),
                                  reset_button_(BOOT_BUTTON_PIN),
                                  touch_button_(TOUCH_BUTTON_PIN, 0.09f)
    {
        vTaskDelay(3000 / portTICK_PERIOD_MS);
        InitializeSystemPowerPin();
        InitializePowerSaveTimer();
        InitializePowerManager();
        reseter = new SystemReset();

        // LCD Init
        InitializeSpi();
        InitializeSt7789Display();
        LVGLInit();
        LVGLMountSpiffs();

        // Show emoji in full screen
        lvgl_port_lock(0);
        if (!lv_scr_act())
        {
            ESP_LOGE(TAG, "No active screen!");
            return;
        }
        emoji = lv_gif_create(lv_scr_act());
        lv_obj_center(emoji);
        lvgl_port_unlock();
        SetDisEmoji(EmojiType::Wakeup);
        TimerHandle_t wakeup_timer_ = xTimerCreate("OneShotTimer", pdMS_TO_TICKS(10000), pdFALSE, this, wakeup_gif_timer_callback);
        xTimerStart(wakeup_timer_, 0);
        PowerOn();

        codec_hal = CodecInit();
        adc_hal = AdcInit();
        audio_hal_ctrl_codec(codec_hal, AUDIO_HAL_CODEC_MODE_DECODE, AUDIO_HAL_CTRL_START);
        InitializeButtons();
        ESP_LOGI(TAG, "Initializing Esp32S3MokeAICarWifiBoard Board");
    }

    void Setvolume(int vol)
    {
        audio_hal_set_volume(codec_hal, vol);
        ESP_LOGI(TAG, "Setvolume %d", vol);
    }

    void Getvolume(int *vol)
    {
        audio_hal_get_volume(codec_hal, vol);
        ESP_LOGD(TAG, "Getvolume %d", *vol);
    }

    virtual Backlight *GetBacklight() override
    {
        static PwmBacklight backlight(GPIO_NUM_42, true);
        return &backlight;
    }

    void SetDisEmoji(void *lv_obj, const char *img_path)
    {
        if (strcmp(img_path, "Listening") == 0)
        {
            SetDisEmoji(EmojiType::Listening);
        }
        else if (strcmp(img_path, "Thinking") == 0)
        {
            SetDisEmoji(EmojiType::Thinking);
        }
        else if (strcmp(img_path, "NetworkConfiging") == 0)
        {
            //SetDisEmoji(EmojiType::NetworkConfiging);
            
        }
        else if (strcmp(img_path, "NetworkConnecting") == 0)
        {
            //SetDisEmoji(EmojiType::NetworkConnecting);
        }
        else if (strcmp(img_path, "NetworkErr") == 0)
        {
            //SetDisEmoji(EmojiType::NetworkErr);
        }
        
        else if (strcmp(img_path, "Sleeping") == 0)
        {
            SetDisEmoji(EmojiType::Sleeping);
        }
        else 
        {
            SetDisEmoji(EmojiType::Default);
        }

    }

    void SetDisEmoji(EmojiType type)
    {
        lvgl_port_lock(0);

        const char *gif_path = nullptr;
        switch (type)
        {
        case EmojiType::Wakeup:
            gif_path = "A:image/wakeup.gif";
            break;
        case EmojiType::Sleeping:
            gif_path = "A:image/sleeping.gif";
            break;
        case EmojiType::Listening:
            gif_path = "A:image/listening.gif";
            break;
        case EmojiType::Thinking:
            gif_path = "A:image/thinking.gif";
            break;
        case EmojiType::NetworkConfiging:
            gif_path = "A:image/netcfing.gif";
            break;
        case EmojiType::NetworkConnecting:
            gif_path = "A:image/netcning.gif";
            break;
        case EmojiType::NetworkErr:
            gif_path = "A:image/neterr.gif";
            break;
        default:
            gif_path = "A:image/default.gif";
            break;
        }

        if (emoji)
        {
            ESP_LOGI(TAG, "Update emoji");
            lv_gif_set_src(emoji, gif_path);
        }

        lvgl_port_unlock();
    }

    void SetDisText(void *lv_obj, const char *text)
    {
    }

    virtual void SetPowerSaveMode(bool enabled) override
    {
        if (!enabled)
        {
            power_save_timer_->WakeUp();
        }
        WifiBoard::SetPowerSaveMode(enabled);
    }

    virtual Stepper *GetStpper(uint32_t stpper_idx) override
    {
        static drv8834 stepper(DRV8834_DIR_PIN, DRV8834_STEP_PIN, DRV8834_EN_PIN);
        return &stepper;
    }
};

DECLARE_BOARD(Esp32S3MokeAICarWifiBoard)
