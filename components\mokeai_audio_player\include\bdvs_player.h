/**
 * Copyright (2017) Baidu Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * File: bdvs_player.h
 * Auth: Lisong Xiao (<EMAIL>)
 * Desc: bdvs player
 */

#ifndef BDVS_PLAYER_H
#define BDVS_PLAYER_H

#ifdef __cplusplus
extern "C" {
#endif

// seek时，表示状态是goto
#define PLAYER_SEEK_GOTO (1 << 16)

typedef enum {
    PLAYER_STATUS_IDLE,
    PLAYER_STATUS_PLAYING,
    PLAYER_STATUS_PAUSED,
    PLAYER_STATUS_STOPING,
    PLAYER_STATUS_STOPED
}PLAYER_STATUS;

typedef enum {
    PLAYER_URL_TYPE_TTS = 0,
    PLAYER_URL_TYPE_MUSIC,
    PLAYER_URL_TYPE_TONE,
    PLAYER_URL_TYPE_MAX
} PLAYER_URL_TYPE;

typedef struct bdvs_player_opts_t{
    void * priv;

    int (*play)(struct bdvs_player_opts_t *opts, int url_type, char * url);     // type = PLAYER_URL_TYPE
    int (*pause)(struct bdvs_player_opts_t *opts);
    int (*resume)(struct bdvs_player_opts_t *opts);
    int (*stop)(struct bdvs_player_opts_t *opts);
    int (*seek)(struct bdvs_player_opts_t *opts, int sec_offset);
    int (*set_volume)(struct bdvs_player_opts_t *opts, int volume);
    int (*get_volume)(struct bdvs_player_opts_t *opts, int * volume);
    int (*play_pcm_data)(struct bdvs_player_opts_t *opts, char * data, int size);
    int (*get_player_status)(struct bdvs_player_opts_t *opts);
    int (*play_list_empty)(struct bdvs_player_opts_t *opts);
} bdvs_player_opts_t;

bdvs_player_opts_t * bdvs_player_create();

#ifdef __cplusplus
}
#endif

#endif
