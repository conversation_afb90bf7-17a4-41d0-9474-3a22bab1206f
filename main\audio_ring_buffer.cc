#include "audio_ring_buffer.h"
#include "esp_log.h"
#include "esp_heap_caps.h"
static const char *TAG = "AudioRingBuffer";
#if 1
void SpiramDeleter::operator()(uint8_t* p) const {
    if(p) heap_caps_free(p);
}
#endif

AudioRingBuffer::AudioRingBuffer(size_t item_count, size_t item_size)
    : buffer_(item_count), capacity_(item_count), item_size_(item_size) {
    #if 1
    for(auto& item : buffer_) {
        uint8_t* mem = (uint8_t*)heap_caps_malloc(item_size_, MALLOC_CAP_SPIRAM);
        if(!mem) {
            ESP_LOGE("AudioRingBuffer", "SPIRAM allocation failed");
            abort();
        }
        item.data = std::unique_ptr<uint8_t[], SpiramDeleter>(mem);
    }
    #else
    for(auto& item : buffer_) {
        item.data = std::make_unique<uint8_t[]>(item_size);
        item.size = item_size;
    }
    #endif

}

bool AudioRingBuffer::Push(const void* data, size_t size) {
    if (!data || size == 0 || size > item_size_) {
        ESP_LOGE(TAG, "Invalid push params");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (full_) {
        // 覆盖最旧数据（head_位置）
        memcpy(buffer_[head_].data.get(), data, size);
        buffer_[head_].size = size;
        
        // 移动head和tail指针
        head_ = (head_ + 1) % capacity_;
        tail_ = (tail_ + 1) % capacity_;
        
        ESP_LOGD(TAG, "Overwrite@%d data=%p", head_, data);
    } else {
        // 常规写入
        memcpy(buffer_[tail_].data.get(), data, size);
        buffer_[tail_].size = size;
        tail_ = (tail_ + 1) % capacity_;
        full_ = (tail_ == head_);
        
        ESP_LOGD(TAG, "Append@%d data=%p", tail_, data);
    }
    return true;
}
bool AudioRingBuffer::Pop(AudioBufferItem& output) {
    if (Empty()) return false;

    std::lock_guard<std::mutex> lock(mutex_);

    // 分配SPIRAM内存的正确方式
    #if 1
    uint8_t* mem = (uint8_t*)heap_caps_malloc(item_size_, MALLOC_CAP_SPIRAM);
    if(!mem) {
        ESP_LOGE("AudioBuffer", "SPIRAM allocation failed for size: %zu", item_size_);
        abort(); // 或处理分配失败情况
    }

    output.data = std::unique_ptr<uint8_t[], SpiramDeleter>(mem);
    #else
    // 深拷贝替代移动语义
    //output.data = std::make_unique<uint8_t[]>(item_size_);
    #endif
    memcpy(output.data.get(), buffer_[head_].data.get(), buffer_[head_].size);
    output.size = buffer_[head_].size;
    // 保持原数据有效
    buffer_[head_].size = 0;  // 标记为已消费
    
    bool was_full = full_;
    head_ = (head_ + 1) % capacity_;
    full_ = false;
    
    ESP_LOGD(TAG, "Pop@%d->%d (was_full:%d)", 
            (head_ + capacity_ - 1) % capacity_, 
            head_, was_full);
    return true;
}

size_t AudioRingBuffer::Size() const {
    return full_ ? capacity_ : 
          (tail_ >= head_) ? (tail_ - head_) : 
          (capacity_ - head_ + tail_);
}

bool AudioRingBuffer::Empty() const {
    return !full_ && (head_ == tail_);
}

bool AudioRingBuffer::Full() const {
    return full_;
}

void AudioRingBuffer::Clear() {
    head_ = tail_ = 0;
    full_ = false;
}
