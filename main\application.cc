#include "application.h"
#include "base_board.h"
#include "system_info.h"
#include "ml307_ssl_transport.h"
#include "mqtt_protocol.h"
#include "mokeai_protocol.h"
#include "audio_recorder.h"

#include <cstring>
#include <esp_log.h>
#include <cJSON.h>
#include <driver/gpio.h>
#include <arpa/inet.h>
#include <esp_app_desc.h>
#include <nvs_flash.h>
#include <led.h>
#include <led/circular_strip.h>
#include <audio_tone_uri.h>
#include <esp_timer.h>
#include <device_status_manager.h>
#include <esp_mac.h> 
#include <system_info.h>
#include <cJSON.h>

#define TAG "Application"
//#define DEBUG_VOICE_REC

extern "C" esp_err_t rec_engine_cb(audio_rec_evt_t *event, void *user_data)
{
    auto &app = Application::GetInstance();
    auto &dev_state_manager = DeviceStatusManger::GetInstance();
    DeviceStateMsg_t msg = {STATE_WAKEUP, nullptr};
    auto &protocol_ = app.protocol_;
    static bool wakeuped = false;
    static bool vad_started = false;
        
    if (protocol_->GetConnectionState() == kConnectionStateConnecting)
    {
        ESP_LOGI(TAG, "Websocket is connecting, wait for connection");
        return ESP_OK;
    }

    if (audio_rec_evt_t::AUDIO_REC_WAKEUP_START == event->type)
    {
        ESP_LOGW(TAG, "AUDIO_REC_WAKEUP_START");
        static uint32_t vad_open_ms = 1000;
        
        msg.state = STATE_OPEN_STREAM_AUDIO_CH;
        msg.state_data = &vad_open_ms;
        dev_state_manager.SetDeviceStatus(&msg);

        wakeuped = true;
        msg.state = STATE_WAKEUP;
        msg.state_data = nullptr;
        dev_state_manager.SetDeviceStatus(&msg);
    }
    else if (audio_rec_evt_t::AUDIO_REC_VAD_START == event->type) 
    {
        #if 0
        if (wakeuped)
        {
            wakeuped = false;
            vad_started = true;
            return ESP_OK;
        }
        #endif    
        if ((protocol_->GetServerRetCode() == PROT_DEV_STOP_CODE && protocol_->GetServerMsgType() == "noInterrupt") ||
            (protocol_->GetServerRetCode() == PROT_TIMEOUT_CODE && protocol_->GetServerMsgType() == "exit"))
            return ESP_OK;

        msg.state = STATE_LISTENING;
        msg.state_data = nullptr;
        dev_state_manager.SetDeviceStatus(&msg);
        ESP_LOGW(TAG, "AUDIO_REC_VAD_START");

    } else if (audio_rec_evt_t::AUDIO_REC_VAD_END == event->type) 
    {
        #if 0
            if (vad_started)
            {
                vad_started = false;
                return ESP_OK;
            }
            #endif
           if ((protocol_->GetServerRetCode() == PROT_DEV_STOP_CODE && protocol_->GetServerMsgType() == "noInterrupt") ||
            (protocol_->GetServerRetCode() == PROT_TIMEOUT_CODE && protocol_->GetServerMsgType() == "exit"))
            return ESP_OK;
        // 需要延迟处理，等缓存区内的数据都发完，在触发结束
        if (app.is_dev_sleep == false)
        {
            xEventGroupClearBits(app.GetEventGroupHandle(), VAD_STARTD);
            xEventGroupSetBits(app.GetEventGroupHandle(), VAD_STOPD);
        }
        ESP_LOGW(TAG, "AUDIO_REC_VAD_END");
    } else if (audio_rec_evt_t::AUDIO_REC_WAKEUP_END == event->type) 
    {
        ESP_LOGW(TAG, "AUDIO_REC_WAKEUP_END");
        msg.state = STATE_SLEEPING;
        msg.state_data = nullptr;
        dev_state_manager.SetDeviceStatus(&msg);

    } else if (audio_rec_evt_t::AUDIO_REC_COMMAND_DECT <= event->type) 
    {
        ESP_LOGI(TAG, "AUDIO_REC_COMMAND_DECT");
    } 
    else 
    {

    }
    return ESP_OK;
}

void Application::vad_open_timer(void)
{
    audio_recorder_vad_check_enable(recorder_->recorder_engine, true);
}
Application::Application() 
{
    lbs_report_msg =  (char *)heap_caps_malloc(500, MALLOC_CAP_SPIRAM);

    // 创建VAD定时打开定时器
    esp_timer_create_args_t timer_args = {
        .callback = [](void* arg) {
            Application* self = static_cast<Application*>(arg);
            self->vad_open_timer();
        },
        .arg = this,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "vad_start_timer",
        .skip_unhandled_events = true,
    };
    ESP_ERROR_CHECK(esp_timer_create(&timer_args, &timer_handle_));
}

Application::~Application() 
{

}

void Application::voice_send_task()
{
    auto &dev_state_manager = DeviceStatusManger::GetInstance();
    DeviceStateMsg_t msg = {STATE_WAKEUP, nullptr};

    AudioBufferItem audio_item;
    bool running = true;
    int wait_time = portMAX_DELAY;
    //static int count = 0;
    while(running)
    {
        EventBits_t bits = xEventGroupWaitBits(
            event_group_handle_,  
            VAD_STARTD | VAD_STOPD,
            pdFALSE,             
            pdFALSE, 
            wait_time
        );
        
        if(bits & VAD_STARTD)
        {
            if (audio_cache->Pop(audio_item))
            {
                ESP_LOGI(TAG, "Send audio data");
                std::vector<uint8_t> audio_data(audio_item.data.get(), audio_item.data.get() + audio_item.size);
                protocol_->SendAudio(audio_data);
            }
            else
            {
                ESP_LOGI(TAG, "No audio data");
                vTaskDelay(pdMS_TO_TICKS(100));
            }
        }
        if (bits & VAD_STOPD)
        {
            // VAD已经结束了，查看当前缓冲区的数据，看有多少，都发出
            if ((!audio_cache->Empty()))
            {
                // 停止录音
                //xEventGroupClearBits(event_group_handle_, WAKEUP_REC_READING);
                ESP_LOGI(TAG, "vad end poping...audio_cache->Size() = %d", audio_cache->Size());
                for(int i=0;  i<audio_cache->Size(); i++)
                {
                    audio_cache->Pop(audio_item);
                    ESP_LOGI(TAG, "vad end sending...");
                    std::vector<uint8_t> audio_data(audio_item.data.get(), audio_item.data.get() + audio_item.size);
                    protocol_->SendAudio(audio_data);
                }
                // 发出发去后，告诉服务器VAD结束了
                msg.state = STATE_THINKING;
                msg.state_data = nullptr;
                dev_state_manager.SetDeviceStatus(&msg);
                xEventGroupClearBits(event_group_handle_, VAD_STOPD);
                // 恢复录音
                //xEventGroupSetBits(event_group_handle_, WAKEUP_REC_READING);
            }
            else
            {
                xEventGroupClearBits(event_group_handle_, VAD_STOPD);
                msg.state = STATE_THINKING;
                msg.state_data = nullptr;
                dev_state_manager.SetDeviceStatus(&msg);
            }

        }
    }
    vTaskDelete(NULL);
}


void Application::voice_read_task()
{
    const int voice_data_read_sz = 1024;
    uint8_t* voice_data_send = (uint8_t*)audio_calloc(1, voice_data_read_sz);
    bool running = true;
    while(running)
    {
        EventBits_t bits = xEventGroupWaitBits(
            event_group_handle_,  
            WAKEUP_REC_READING ,
            pdFALSE,
            pdFALSE,
            portMAX_DELAY
        );
        if(bits & WAKEUP_REC_READING)
        {
            int ret = audio_recorder_data_read(recorder_->recorder_engine,
                voice_data_send, voice_data_read_sz, portMAX_DELAY);
            if(ret <= 0) continue;

            #if defined DEBUG_VOICE_REC
            if (audio_file_1) {
                fwrite(voice_data_send, 1, voice_data_read_sz, audio_file_1);
            }
            #endif
            audio_cache->Push(voice_data_send, voice_data_read_sz);
        }
        
    }
    audio_free(voice_data_send);
    vTaskDelete(NULL);
}

void Application::display_task() {
    auto & board = Board::GetInstance();
    DeviceDisplayMsg msg;
    auto * led   = board.GetLed();
    auto * v_stepper = board.GetStpper(0);
    auto * h_stepper = board.GetStpper(1);
    char * text = (char *)heap_caps_malloc(200, MALLOC_CAP_SPIRAM);
    while (true)
    {
        if (xQueueReceive(display_queue_, &msg, portMAX_DELAY) == pdTRUE)
        {
            switch (msg.state) 
            {
                case DISPLAY_STATE_WAKEUP:
                {
                    if (board.GetBoardName() == "mokeai_desk")
                        board.SetDisText((void *)2, NULL);
                    board.SetDisEmoji(nullptr, "Listening");
                    v_stepper->enable();
                    v_stepper->set_direction(1);
                    v_stepper->step(500);
                    v_stepper->set_direction(0);
                    v_stepper->step(500);
                    v_stepper->set_direction(0);
                    v_stepper->step(500);
                    v_stepper->set_direction(1);
                    v_stepper->step(500);
                    v_stepper->disable();
                }
                break;
                case DISPLAY_STATE_SLEEPING:
                    board.SetDisEmoji(nullptr, "Sleeping");
                break;
                case DISPLAY_STATE_LISTENING:
                    if (board.GetBoardName() == "mokeai_desk")
                    {
                        board.SetDisText((void *)2, NULL);
                        h_stepper->enable();
                        h_stepper->set_direction(1);
                        h_stepper->step(2000);
                        h_stepper->disable();
                    }
                    board.SetDisEmoji(nullptr, "Listening");
                break;
                case DISPLAY_STATE_THINKING:
                    if (board.GetBoardName() == "mokeai_desk")
                    {
                        h_stepper->enable();
                        h_stepper->set_direction(0);
                        h_stepper->step(1000);
                        h_stepper->disable();
                    }
                    board.SetDisEmoji(nullptr, "Thinking");
                break;
                case DISPLAY_STATE_NETWORK_CONFIG:
                    ESP_LOGI(TAG, "NetworkConfiging");
                    strcpy(text, "NetworkConfiging");
                    board.SetDisEmoji(nullptr, text);
                break;
                case DISPLAY_STATE_NETWORK_CONNECTING:
                    board.SetDisEmoji(nullptr, "NetworkConnecting");
                break;
                case DISPLAY_STATE_BAT_LOW:
                {
                    struct StripColor high = {50, 0, 0};
                    led->SetBlink(high, 100);
                }
                break;
                default:
                    break;
            }
        }
    }
}


void Application::device_status_mon_task(void)
{
    auto & board = Board::GetInstance();
    auto * led   = board.GetLed();
    auto &dev_sta_manger = DeviceStatusManger::GetInstance();
    auto * v_stepper = board.GetStpper(0);
    auto * h_stepper = board.GetStpper(1);

    char filename[64];
    DeviceStateMsg_t msg;
    DeviceDisplayMsg display_msg;
    static bool error_exit = false;
    static bool mananel_exit = false;
    while (true) {
        if (xQueueReceive(dev_sta_manger.GetDeviceStatusQueue(), &msg, portMAX_DELAY) == pdTRUE)
        {
            switch (msg.state) {
                case STATE_DISCONNECTED:
                    {
                        ESP_LOGW(TAG, "Device is disconnected");
                        if (board.GetBoardName() == "mokeai_desk")
                            board.SetDisText((void *)0, (const char *)msg.state_data);
                        display_msg.state = DISPLAY_STATE_NETWORK_CONFIG;
                        display_msg.state_data = nullptr;
                        if (xQueueSend(display_queue_, &display_msg, 0) != pdPASS) 
                        {
                            ESP_LOGE(TAG, "set device state %d failed", display_msg.state);
                        }
                        struct StripColor high = {0, 0, 255};
                        led->SetBlink(high, 500);
                    }
                    break;
                case STATE_NETWORK_CONFIG:
                {
                    display_msg.state = DISPLAY_STATE_NETWORK_CONNECTING;
                    display_msg.state_data = nullptr;
                    if (xQueueSend(display_queue_, &display_msg, 0) != pdPASS) 
                    {
                        ESP_LOGE(TAG, "set device state %d failed", display_msg.state);
                    }
                    struct StripColor high = {0, 0, 255};
                    led->SetBlink(high, 300);
                    ESP_LOGW(TAG, "Device is in network configuration mode");
                }
                break;
                case STATE_NETWORK_SUCCESS:
                    {
                        ESP_LOGW(TAG, "Device network connection successful");
                        PlaySound(get_tone_url(TONE_NET_LINKED));
                        //board.SetDisEmoji(nullptr, "network_success");
                    }
                    break;
                case STATE_NETWORK_FAILED:
                    {
                        //board.SetDisEmoji(nullptr, "network_failed");
                        ESP_LOGW(TAG, "Device network connection failed");
                    }
                    break;
                case STATE_NETWORK_SCANNING:
                    {
                        //board.SetDisEmoji(nullptr, "network_scanning");
                        ESP_LOGW(TAG, "Device is scanning for networks");
                        struct StripColor high = {0, 0, 255};
                        led->SetBlink(high, 300);
                    }
                    break;
                case STATE_NETWORK_CONNECTING_SERVER_FAILED:
                    {
                        //board.SetDisEmoji(nullptr, "network_failed");
                        ESP_LOGW(TAG, "Device connecting  serivce  failed");
                    }
                    break;
                case STATE_AUTHENTICATING:
                    {
                        //board.SetDisEmoji(nullptr, "auth_waiting");
                        ESP_LOGW(TAG, "Device is authenticating");
                    }
                    break;
                case STATE_AUTH_SUCCESS:
                    {
                        struct StripColor high = {0, 0, 255};
                        led->SetStaticColor(high);
                        PlaySound(get_tone_url(TONE_DEV_AUTH_SUCC));
                        //board.SetDisEmoji(nullptr, "auth_success");
                        ESP_LOGW(TAG, "Device authentication successful");
                    }
                    break;
                case STATE_AUTH_FAILED:
                    {
                        PlaySound(get_tone_url(TONE_DEV_AUTH_FAILED));
                        board.SetDisEmoji(nullptr, "auth_failed");
                        ESP_LOGW(TAG, "Device authentication failed");
                    }
                    break;
                case STATE_WAKEUP:
                {
                    board.SetPowerSaveMode(false);
                    if (board.GetBoardType() == "ml307")
                        xEventGroupClearBits(event_group_handle_, READ_DEV_LSB_INFO);
                    xEventGroupClearBits(event_group_handle_, VAD_STARTD | WAKEUP_REC_READING);
                    #if defined DEBUG_VOICE_REC
                    audio_file_1 = fopen("/sdcard/long.pcm", "wb+");
                    if (audio_file_1 == NULL) {
                        ESP_LOGI(TAG, "Cannot open file, reason: %s\n", strerror(errno));
                    }
                    #endif
                    // 本地测试需要大概500ms才能等到认证消息
                    // (4G版本)服务器大概100ms以内
                    int retry_count = 1000;
                    CloseAudioPlayerStreamPIPE();
                    error_exit = false;
                    is_dev_sleep = false;
                    xEventGroupSetBits(event_group_handle_, WAKEUP_REC_READING);
                    do
                    {
                        vTaskDelay(pdMS_TO_TICKS(1));
                    } while (protocol_->GetAuthenticationState() != kAuthenticationStateAuthenticated && retry_count--);
                    if (protocol_->GetAuthenticationState() != kAuthenticationStateAuthenticated || retry_count <= 0)
                    {
                        PlaySound(get_tone_url(TONE_CON_SERVICE_TIMEOUT));
                        ESP_LOGE(TAG, "Device authentication failed, retrying...%d %d", protocol_->GetAuthenticationState(), retry_count);
                        retry_count = 1000;
                        continue;
                    }
                    OpenAudioPlayerStreamPIPE();
                    display_msg.state = DISPLAY_STATE_WAKEUP;
                    display_msg.state_data = nullptr;
                    if (xQueueSend(display_queue_, &display_msg, 0) != pdPASS) 
                    {
                        ESP_LOGE(TAG, "set device state %d failed", display_msg.state);
                    }
                    protocol_->SendWakeWordDetected("你好，小智");
                    // LED提示用户已经被唤醒
                    struct StripColor low  = {0, 0, 0};
                    struct StripColor high = {0, 0, 255};
                    led->SetFlowEffect(high, 100);
                    led->SetBreathe(low, high, 5);
                    ESP_LOGW(TAG, "Device is waking up");
                }
                break;
                // 退出且关闭通道
                case STATE_ABORTED:
                {
                    error_exit = true;
                    if (is_dev_sleep == false)
                        audio_recorder_trigger_stop(recorder_->recorder_engine);
                }
                break;
                case STATE_SERVER_RSP_SUCCECCED:
                {
                    
                }
                break;
                case STATE_SERVER_RSP_ASR_TXT:
                {
                    if (board.GetBoardName() == "mokeai_desk")
                        board.SetDisText((void *)0, (const char *)msg.state_data);
                }
                break;

                case STATE_SERVER_RSP_TTS_TXT:
                {
                    if (board.GetBoardName() == "mokeai_desk")
                        board.SetDisText((void *)1, (const char *)msg.state_data);
                }
                break;
                // 退出且等待音频结束
                case STATE_ABORTED_WAIT_AUDIO_END:
                break;
                // 正常退出
                case STATE_SLEEPING:
                {
                    is_dev_sleep = true;
                    xEventGroupClearBits(event_group_handle_, VAD_STARTD | WAKEUP_REC_READING);
                    display_msg.state = DISPLAY_STATE_SLEEPING;
                    display_msg.state_data = nullptr;
                    if (xQueueSend(display_queue_, &display_msg, 0) != pdPASS) 
                    {
                        ESP_LOGE(TAG, "set device state %d failed", display_msg.state);
                    }
                    audio_cache->Clear();
                    #if defined DEBUG_VOICE_REC
                    if (audio_file_1)
                    {
                        ESP_LOGI(TAG, "Closing audio file: %s", filename);
                        fclose(audio_file_1);
                        audio_file_1 = nullptr;
                    }
                    #endif
                    struct StripColor high = {0, 0, 255};
                    led->SetStaticColor(high);
                    // 正常休眠退出，需要告诉服务器，异常退出和手动退出不需要
                    if (error_exit == false && mananel_exit == false)
                        protocol_->SendAutoExit();
                    // 手动退出
                    if (mananel_exit)
                    {
                        protocol_->SendManualExit();
                        CloseAudioPlayerStreamPIPE();
                        mananel_exit = false;
                    }
                    board.SetPowerSaveMode(true);
                    if (board.GetBoardType() == "ml307")
                        xEventGroupSetBits(event_group_handle_, READ_DEV_LSB_INFO);
                    ESP_LOGW(TAG, "Device is sleeping");
                }
                break;
                case STATE_LISTENING:
                {
                    #if defined DEBUG_VOICE_REC
                    memset(filename, 0, sizeof(filename));
                    time_t now = time(nullptr);
                    struct tm *t = localtime(&now);
                    snprintf(filename, sizeof(filename), 
                        "%s%02d%02d%02d.pcm","/sdcard/",
                        t->tm_hour, t->tm_min, t->tm_sec);
                    ESP_LOGI(TAG, "Creating audio file: %s", filename);
                     
                    audio_file = fopen(filename, "wb+");
                    if (audio_file == NULL) {
                        ESP_LOGI(TAG, "Cannot open file, reason: %s\n", strerror(errno));
                    }
                    #endif
                    display_msg.state = DISPLAY_STATE_LISTENING;
                    display_msg.state_data = nullptr;
                    if (xQueueSend(display_queue_, &display_msg, 0) != pdPASS) 
                    {
                        ESP_LOGE(TAG, "set device state %d failed", display_msg.state);
                    }

                    CloseAudioPlayerStreamPIPE();
                    protocol_->SendStartListening(kListeningModeAlwaysOn);
                    xEventGroupSetBits(event_group_handle_, VAD_STARTD);
                    ESP_LOGW(TAG, "Device is listening");
                }
                break;
                case STATE_THINKING:
                {
                    display_msg.state = DISPLAY_STATE_THINKING;
                    display_msg.state_data = nullptr;
                    if (xQueueSend(display_queue_, &display_msg, 0) != pdPASS) 
                    {
                        ESP_LOGE(TAG, "set device state %d failed", display_msg.state);
                    }
                    // 延时打开，等task id 变过来
                    //OpenAudioPlayerStreamPIPE();
                    xEventGroupClearBits(event_group_handle_, VAD_STARTD);
                    if (audio_file)
                    {
                        ESP_LOGI(TAG, "Closing audio file: %s", filename);
                        fclose(audio_file);
                        audio_file = nullptr;
                    }
                    protocol_->SendStopListening();
                    ESP_LOGW(TAG, "Device is thinking");
                }
                break;
                case STATE_COMMAND_DONE:
                    ESP_LOGW(TAG, "Device command done");
                    break;
                case STATE_RESULT_READY:
                    ESP_LOGW(TAG, "Device result ready");
                    break;
                case STATE_BUTTON_CLICK:
                {
                    if (strcmp((char *)msg.state_data, "reset_button") == 0)
                        ESP_LOGW(TAG, "reset_button click");
                    else if (strcmp((char *)msg.state_data, "touch_button") == 0)
                    {
                        ESP_LOGW(TAG, "touch_button click");
                        if (is_dev_sleep == false)
                        {
                            mananel_exit = true;
                            audio_recorder_trigger_stop(recorder_->recorder_engine);
                        }
                        else
                        {
                            protocol_->SendTouchDetect();
                            ESP_LOGW(TAG, "Device in sleep mode touched.");
                        }
                        v_stepper->enable();
                        v_stepper->set_direction(1);
                        v_stepper->step(500);
                        v_stepper->set_direction(0);
                        v_stepper->step(500);
                        v_stepper->set_direction(0);
                        v_stepper->step(500);
                        v_stepper->set_direction(1);
                        v_stepper->step(500);
                        v_stepper->disable();
                    }
                    else
                    {
                        ESP_LOGW(TAG, "others button long press");
                    }
                    break;
                }
                case STATE_BUTTON_LONG_PRESS:
                    if (strcmp((char *)msg.state_data, "reset_button") == 0)
                    {
                        struct StripColor high = {0, 0, 0};
                        led->SetStaticColor(high);
                        board.GetBacklight()->SetBrightness(0, true);
                        ESP_LOGW(TAG, "reset_button long press");
                    }
                    else if (strcmp((char *)msg.state_data, "touch_button") == 0)
                    {
                        ESP_LOGW(TAG, "touch_button long press");
                    }
                    else 
                        ESP_LOGW(TAG, "others button long press");
                    break;
                case STATE_BUTTON_DOUBLE_CLICK:
                    if (strcmp((char *)msg.state_data, "reset_button") == 0)
                        ESP_LOGW(TAG, "reset_button double click");
                    else if (strcmp((char *)msg.state_data, "touch_button") == 0)
                        ESP_LOGW(TAG, "touch_button double click");
                    else
                        ESP_LOGW(TAG, "others button long press");
                    break;
                case STATE_BATT_DSCHARGING:
                    //PlaySound(get_tone_url(TONE_CHARGED));
                    ESP_LOGW(TAG, "battery discharging");
                    break;
                case STATE_BATT_CHARGING:
                    //PlaySound(get_tone_url(TONE_CHARGED));
                    ESP_LOGW(TAG, "battery charging");
                    break;
                case STATE_BATT_CHARGING_DONE:
                    ESP_LOGW(TAG, "battery charging done");
                    break;
                case STATE_BATT_LEVEL_REPOART:
                    ESP_LOGW(TAG, "battery level report");
                    break;
                case STATE_BATT_LOW:
                    display_msg.state = DISPLAY_STATE_BAT_LOW;
                    display_msg.state_data = nullptr;
                    if (xQueueSend(display_queue_, &display_msg, 0) != pdPASS) 
                    {
                        ESP_LOGE(TAG, "set device state %d failed", display_msg.state);
                    }
                    ESP_LOGW(TAG, "battery low");
                    break;
                case STATE_CHECKING_VERSION:
                    ESP_LOGW(TAG, "checking new version");
                    break;
                case STATE_CHECKING_VERSION_FAILED:
                    ESP_LOGW(TAG, "checking new version failed");
                    break;
                case STATE_UPGREADE_SUCCESS:
                    ESP_LOGW(TAG, "upgrade success");
                    break;
                case STATE_UPGARDE_PROCESS_REPORT:
                    ESP_LOGW(TAG, "upgrade process report %s", (char *)msg.state_data);
                    break;
                case STATE_UPGRADING:
                    ESP_LOGW(TAG, "upgrading");
                    break;
                case STATE_SYNC_TIME:
                {
                    ESP_LOGW(TAG, "sync time");
                    uint64_t *timestamp = (uint64_t *)msg.state_data;
                    struct timeval now = 
                    {
                        .tv_sec = time_t (*timestamp), 
                        .tv_usec = 0
                    };
                    if(settimeofday(&now, NULL) != 0) 
                    {
                        ESP_LOGE(TAG, "Failed to set system time");
                        continue;
                    }
                    ESP_LOGW(TAG, "sync system time successed");
                }
                break;
                case STATE_RECONNECTING:
                {
                    // 当出现overflow的问题时，手动进入休眠状态
                    xEventGroupClearBits(event_group_handle_, VAD_STARTD | WAKEUP_REC_READING | VAD_STOPD);
                    CloseAudioPlayerStreamPIPE();
                    ESP_LOGW(TAG, "reconnecting");
                    uint32_t retry_cnt = 3;
                    // 关闭通道，并删除websocket实例
                    protocol_->CloseAudioChannel();
                    // 重新创建实例
                    protocol_->Start();
                    do
                    {
                        protocol_->OpenAudioChannel();
                    }while (retry_cnt-- && protocol_->GetConnectionState() != kConnectionStateConnected);
                    retry_cnt = 3;
                    mananel_exit = true;
                    audio_recorder_trigger_stop(recorder_->recorder_engine);
                }
                break;
                case STATE_OPEN_STREAM_AUDIO_CH:
                {
                    uint32_t *open_timer_ms = (uint32_t *)msg.state_data;
                    audio_recorder_vad_check_enable(recorder_->recorder_engine, false);
                    if (esp_timer_is_active(timer_handle_) == true)
                        esp_timer_stop(timer_handle_);
                    if (open_timer_ms == NULL)
                        ESP_ERROR_CHECK(esp_timer_start_once(timer_handle_, 5000000));
                    else
                        ESP_ERROR_CHECK(esp_timer_start_once(timer_handle_, *open_timer_ms*1000));
                    OpenAudioPlayerStreamPIPE();
                }
                break;
                default:
                    ESP_LOGW(TAG, "Unknown device state: %d", msg.state);
                break;
            }
        }
    }
}
void Application::Start()
{
    display_queue_= xQueueCreate(10, sizeof(DeviceDisplayMsg)); 

    auto & board = Board::GetInstance();
    player_ = create_audio_player();
    recorder_ = wakeup_audio_recorder_init(::rec_engine_cb, 1000, 60000);
    board.Setvolume(40);
    board.GetBacklight()->SetBrightness(100, true);
    
#if 0
         xTaskCreate([](void* arg) {
            Application* app = (Application*)arg;
            app->device_res_monitor_task();
            vTaskDelete(NULL);
        }, "res_monitor_task", 4096, this, 1, nullptr);
#endif

#if 0
    xTaskCreate([](void* arg) {
        Application* app = (Application*)arg;
        app->device_status_mon_task();
        vTaskDelete(NULL);
    }, "device_status_mon_task", 4096, this, 9, nullptr);
#else

    xTaskCreate([](void* arg) {
            Application* app = (Application*)arg;
            app->display_task();
            vTaskDelete(NULL);
        }, "display", 4096, this, 1, nullptr);

    xTaskCreatePinnedToCore([](void* arg) {
    Application* app = (Application*)arg;
    app->device_status_mon_task();
    vTaskDelete(NULL);
    }, "device_status_mon_task", 4096, this, 9, nullptr, 0);
#endif
    event_group_handle_ = xEventGroupCreate();
    board.StartNetwork();
    protocol_ = std::make_unique<WebsocketProtocol>();
    // 流式音频输出到播放器
    protocol_->OnIncomingAudio([this](std::vector<uint8_t>&& data) {
    // 加入缓存机制，以解决唤醒时的音频卡顿问题
    static std::vector<std::vector<uint8_t>> frame_buffer;
    static const size_t max_frames = 2;
    
    if (data.empty() || !player_) return;
    
    // 将新帧加入缓冲区
    frame_buffer.emplace_back(std::move(data));
    
    // 当缓冲区达到2帧时处理
    if (frame_buffer.size() >= max_frames) {
        // 合并所有帧数据
        std::vector<uint8_t> combined;
        for (auto& frame : frame_buffer) {
            combined.insert(combined.end(), frame.begin(), frame.end());
        }
        frame_buffer.clear();

        // 写入播放器
        player_->stream_pipeline->player_pipeline_write(combined.data(), combined.size());
        
    }
});
    protocol_->Start();
    OpenAudioPlayerStreamPIPE();
    if (protocol_->DeviceAuthenticate() == true)
    {
        CheckNewVersion();
        xTaskCreate([](void* arg) {
            Application* app = (Application*)arg;
            app->voice_read_task();
            vTaskDelete(NULL);
        }, "voice_read_task", 4096, this, 4, nullptr);
        xTaskCreate([](void* arg) {
            Application* app = (Application*)arg;
            app->voice_send_task();
            vTaskDelete(NULL);
        }, "voice_send_task", 4096, this, 6, nullptr);

        PlaySound(get_tone_url(TONE_WELCOME));
        vTaskDelay(pdMS_TO_TICKS(5000));
        recorder_->pipeline->run(recorder_->pipeline);
        if (board.GetBoardType() == "ml307")
        {
                xTaskCreate([](void* arg) {
                Application* app = (Application*)arg;
                app->device_lbs_read_task();
                vTaskDelete(NULL);
            }, "dev_libs_read_task", 4096, this, 1, nullptr);

             xTaskCreate([](void* arg) {
                Application* app = (Application*)arg;
                app->device_lbs_report_task();
                vTaskDelete(NULL);
            }, "dev_libs_rpt_task", 4096, this, 2, nullptr);
            xEventGroupSetBits(event_group_handle_, READ_DEV_LSB_INFO);
        }
        board.SetDisEmoji(nullptr, "Sleeping");
    }
    else
    {
        PlaySound(get_tone_url(TONE_DEV_AUTH_FAILED));
        CloseAudioPlayerStreamPIPE();
    }
    
}

void Application::device_lbs_report_task(void)
{
    std::string lbs_report_url = "https://yiqichuangrobot.com/robotProd-api/hardware/address/updateLatitudeLongitude";
    while (1)
    {
        EventBits_t bits = xEventGroupWaitBits(
            event_group_handle_,  
            REPORT_DEV_LSB_INFO ,
            pdFALSE,
            pdFALSE,
            portMAX_DELAY
        );
        if(bits & REPORT_DEV_LSB_INFO)
        {
            if (!is_dev_sleep)
            {
                xEventGroupClearBits(event_group_handle_, REPORT_DEV_LSB_INFO | READ_DEV_LSB_INFO);
                ESP_LOGD(TAG, "device in wakeup mode stop report lbs info");
                continue;
            }
            auto http = Board::GetInstance().CreateHttp();
            // 设置http 请求头
            http->SetHeader("accept", "*/*");
            http->SetHeader("Content-Type", "application/json");
            // 开始请求
            if (!http->Open("POST", lbs_report_url, lbs_report_msg)) 
            {
                ESP_LOGE(TAG, "report libs info[%s] to [%s]failed", lbs_report_msg, lbs_report_url.c_str());
            }
            else
            {
                ESP_LOGI(TAG, "report libs info[%s] to [%s] successed", lbs_report_msg, lbs_report_url.c_str());
            }
            // 清除连接
            delete http;
            xEventGroupClearBits(event_group_handle_, REPORT_DEV_LSB_INFO);
            
        }
    }
}

bool  Application::check_device_location_distance(const float distance, const Coordinate *point1, const Coordinate *point2)
{
    // 转换为弧度
    double lat1_rad = point1->lat * DEG_TO_RAD;
    double lon1_rad = point1->lon * DEG_TO_RAD;
    double lat2_rad = point2->lat * DEG_TO_RAD;
    double lon2_rad = point2->lon * DEG_TO_RAD;
    
    // 差值计算
    double dlat = lat2_rad - lat1_rad;
    double dlon = lon2_rad - lon1_rad;
    
    // Haversine公式
    double a = sin(dlat/2) * sin(dlat/2) + 
               cos(lat1_rad) * cos(lat2_rad) * 
               sin(dlon/2) * sin(dlon/2);
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    double distance_ = EARTH_RADIUS * c;
    ESP_LOGI(TAG,"point1 (%f, %f) point2(%f, %f) distance: %f", point1->lat, point1->lon, point2->lat, point2->lon, distance_);
    return distance_ <= distance;
}
void Application::device_lbs_read_task(void)
{
    auto &board  = Board::GetInstance();
    double longitude , latitude;
    double prev_longitude = 0, prev_latitude = 0;
    std::string mac_address = SystemInfo::GetMacAddress();
    uint32_t wait_time_ms = 10000; // 如果失败，30s读取一次
    // 先读取一次，第一次一定会失败
    board.GetLocation(latitude,longitude);
    vTaskDelay(pdMS_TO_TICKS(6000));
    while (1)
    {
         EventBits_t bits = xEventGroupWaitBits(
            event_group_handle_,  
            READ_DEV_LSB_INFO ,
            pdFALSE,
            pdFALSE,
            portMAX_DELAY
        );
        if (bits & READ_DEV_LSB_INFO)
        {
            if (board.GetLocation(latitude,longitude))
            {
                ESP_LOGI(TAG, "longitude: %.8f, latitude: %.8f", longitude, latitude);
                // 只有当位置发生改变时，才触发上报操作
                if (longitude == prev_longitude && latitude == prev_latitude)
                {
                    ESP_LOGW(TAG, "locations are same not updates");
                    goto end;
                }
                else
                {
                    // 检查是否两个经纬度值，是否在100m内
                    Coordinate point1 = {longitude, latitude};
                    Coordinate point2 = {prev_longitude, prev_latitude};
                    if (check_device_location_distance(DISTANCE_THRESHOLD,&point2,&point1))
                    {
                        prev_longitude = longitude;
                        prev_latitude = latitude;
                        // DISTANCE_THRESHOLD 范围内，不上报经纬度
                        ESP_LOGW(TAG, "locations are in 100m not update");
                        goto end;
                    }
                    prev_longitude = longitude;
                    prev_latitude = latitude;
                    ESP_LOGW(TAG, "locations will update");
                }
                cJSON *report_content_json = cJSON_CreateObject();

                if (!report_content_json)
                {
                    ESP_LOGE(TAG, "lbs report json object create failed");
                    goto end;
                }
                cJSON_AddStringToObject(report_content_json, "macId", mac_address.c_str());
                cJSON_AddStringToObject(report_content_json, "longitude", std::to_string(longitude).c_str());
                cJSON_AddStringToObject(report_content_json, "latitude", std::to_string(latitude).c_str());

                char* message = cJSON_PrintUnformatted(report_content_json);

                ESP_LOGI(TAG, "lbs_report_content: %s", message);

                memset(lbs_report_msg, 0, 500);
                strcpy(lbs_report_msg,message);

                if (report_content_json)
                    cJSON_Delete(report_content_json);
                report_content_json = nullptr;
                if (message)
                    cJSON_free(message);
                message = nullptr;
                xEventGroupSetBits(event_group_handle_, REPORT_DEV_LSB_INFO);
            }
            else
            {
                ESP_LOGE(TAG, "read location failed %d", (int)wait_time_ms);
                // 如果开机就读取错误了，那么，上报默认的经纬度(0, 0)
                if (longitude == 0 && latitude == 0)
                {
                    cJSON *report_content_json = cJSON_CreateObject();

                    if (!report_content_json)
                    {
                        ESP_LOGE(TAG, "lbs report json object create failed");
                        goto end;
                    }
                    cJSON_AddStringToObject(report_content_json, "macId", mac_address.c_str());
                    cJSON_AddStringToObject(report_content_json, "longitude", std::to_string(longitude).c_str());
                    cJSON_AddStringToObject(report_content_json, "latitude", std::to_string(latitude).c_str());

                    char* message = cJSON_PrintUnformatted(report_content_json);

                    ESP_LOGI(TAG, "lbs_report_content: %s", message);

                    memset(lbs_report_msg, 0, 500);
                    strcpy(lbs_report_msg,message);

                    if (report_content_json)
                        cJSON_Delete(report_content_json);
                    report_content_json = nullptr;
                    if (message)
                        cJSON_free(message);
                    message = nullptr;
                    
                }
                // 如果读取错误则上报上次的数据
                xEventGroupSetBits(event_group_handle_, REPORT_DEV_LSB_INFO);
            }
end:
            vTaskDelay(pdMS_TO_TICKS(wait_time_ms));
        }
    }
}
void Application::device_res_monitor_task(void)
{
    uint32_t cycle = 0;
    uint8_t mac[6];

    esp_read_mac(mac, ESP_MAC_WIFI_STA);
    printf("WiFi MAC: %02x:%02x:%02x:%02x:%02x:%02x\n",
           mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

    while (1)
    {
        cycle++;
        printf("\n========== System Monitor #%lu ==========\n", cycle);

        printf("Memory Status:\n");
        printf("  Internal RAM: %lu KB free / %lu KB total\n",
               (unsigned long)(heap_caps_get_free_size(MALLOC_CAP_INTERNAL) / 1024),
               (unsigned long)(heap_caps_get_total_size(MALLOC_CAP_INTERNAL) / 1024));

        size_t spiram_total = heap_caps_get_total_size(MALLOC_CAP_SPIRAM);
        if (spiram_total > 0) {
            printf("  SPI RAM: %lu KB free / %lu KB total\n",
                   (unsigned long)(heap_caps_get_free_size(MALLOC_CAP_SPIRAM) / 1024),
                   (unsigned long)(spiram_total / 1024));
        }

        printf("  Min free ever: %lu KB\n", (unsigned long)(esp_get_minimum_free_heap_size() / 1024));
        
        char buffer[2048];
        memset(buffer, 0, sizeof(buffer));
        
        vTaskGetRunTimeStats(buffer);
        
        if (strlen(buffer) > 0) {
            printf("Task Runtime Statistics:\n");
            printf("%-16s %10s %8s\n", "Task", "Abs Time", "% Time");
            printf("------------------------------------\n");
            printf("%s", buffer);
        } else {
            printf("Runtime stats not available\n");
        }

        printf("==========================================\n");
        vTaskDelay(pdMS_TO_TICKS(5000));
    }
}
void Application::CheckNewVersion() {
    const int MAX_RETRY = 3;
    int retry_count = 0;
    int retry_delay = 2; 
    auto &dev_state_manager = DeviceStatusManger::GetInstance();
    DeviceStateMsg_t msg = {STATE_WAKEUP, nullptr};
    auto &board  = Board::GetInstance();

    while (true) {
        
        msg.state = STATE_CHECKING_VERSION;
        msg.state_data = nullptr;
        dev_state_manager.SetDeviceStatus(&msg);
        std::string ota_url = CONFIG_MOKEAI_OTA_URL + board.GetBoardName() + ".bin";
        OtaErrCode ota_ret_code;
        do
        {
            ota_ret_code = ota_.CheckVersion(ota_url);
            retry_count++;
            if (retry_count >= MAX_RETRY) {
                ESP_LOGE(TAG, "Too many retries, exit version check");
                return;
            }

            msg.state = STATE_CHECKING_VERSION_FAILED;
            msg.state_data = nullptr;
            dev_state_manager.SetDeviceStatus(&msg);
            
            ESP_LOGW(TAG, "Check new version failed, retry in %d seconds (%d/%d)", retry_delay, retry_count, MAX_RETRY);
            for (int i = 0; i < retry_delay; i++) {
                vTaskDelay(pdMS_TO_TICKS(1000));
            }
            continue;

        } while (ota_ret_code == OtaErrCode::OTA_ERR_CODE_HTTP_ERROR);
        
        retry_count = 0;
        retry_delay = 2; // 重置重试延迟时间

        if (ota_.HasNewVersion()) {
            msg.state = STATE_UPGRADING;
            msg.state_data = nullptr;
            dev_state_manager.SetDeviceStatus(&msg);
            vTaskDelay(pdMS_TO_TICKS(3000));

            CloseAudioPlayerStreamPIPE();
            ota_.StartUpgrade([ this](int progress, size_t speed) {
                static char buffer[64];
                auto &dev_state_manager = DeviceStatusManger::GetInstance();
                DeviceStateMsg_t msg = {STATE_UPGARDE_PROCESS_REPORT, buffer};

                snprintf(buffer, sizeof(buffer), "%d%% %uKB/s", progress, speed / 1024);
                ESP_LOGI(TAG, "Upgrade progress: %s", buffer);
                dev_state_manager.SetDeviceStatus(&msg);
            });

            ESP_LOGI(TAG, "Firmware upgrade failed...");
            vTaskDelay(pdMS_TO_TICKS(3000));
            Reboot();
            return;
        }

        // No new version, mark the current version as valid
        ota_.MarkCurrentVersionValid();
        if (!ota_.HasActivationCode() && !ota_.HasActivationChallenge()) {
            //xEventGroupSetBits(event_group_, CHECK_NEW_VERSION_DONE_EVENT);
            // Exit the loop if done checking new version
            break;
        }

        //display->SetStatus(Lang::Strings::ACTIVATION);
        // Activation code is shown to the user and waiting for the user to input
        //if (ota_.HasActivationCode()) {
        //    ShowActivationCode();
        //}

        // This will block the loop until the activation is done or timeout
        #if 0
        for (int i = 0; i < 10; ++i) {
            ESP_LOGI(TAG, "Activating... %d/%d", i + 1, 10);
            esp_err_t err = ota_.Activate();
            if (err == ESP_OK) {
                xEventGroupSetBits(event_group_, CHECK_NEW_VERSION_DONE_EVENT);
                break;
            } else if (err == ESP_ERR_TIMEOUT) {
                vTaskDelay(pdMS_TO_TICKS(3000));
            } else {
                vTaskDelay(pdMS_TO_TICKS(10000));
            }
            if (device_state_ == kDeviceStateIdle) {
                break;
            }
        }
        #endif
    }
}