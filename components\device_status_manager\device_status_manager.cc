#include "device_status_manager.h"
#include <freertos/queue.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"

void DeviceStatusManger::SetDeviceStatus(DeviceStateMsg_t *msg)
{

    if (device_status_queue_ != nullptr) 
    {
        if (xQueueSend(device_status_queue_, msg, 0) != pdPASS) 
        {
            ESP_LOGE(TAG, "set device state %d failed", msg->state);
        }
        else
        {
            device_status_ = msg->state;
        }
    }
}

void DeviceStatusManger::SetDeviceStatusFromInterurt(DeviceStateMsg_t *msg)
{

    ESP_LOGI(TAG, "set device state %d from interrupt", msg->state);
    
    if (device_status_queue_ != nullptr) 
    {
        if (xQueueSendFromISR(device_status_queue_, msg, 0) != pdPASS) 
        {
            ESP_LOGE(TAG, "set device state %d failed", msg->state);
        }
        else
        {
            device_status_ = msg->state;
        }
    }
}

DeviceStatusManger::DeviceStatusManger()
{
    device_status_queue_= xQueueCreate(10, sizeof(DeviceStateMsg_t)); 

}
