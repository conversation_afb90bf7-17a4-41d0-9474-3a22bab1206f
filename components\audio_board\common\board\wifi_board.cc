#include "wifi_board.h"

//#include "display.h"
//#include "application.h"
//#include "system_info.h"
//#include "font_awesome_symbols.h"
#include "settings.h"
//#include "assets/lang_config.h"

#include <freertos/FreeRTOS.h>
//#include <frertos/task.h>
#include <esp_http.h>
#include <esp_mqtt.h>
#include <esp_udp.h>
#include <tcp_transport.h>
#include <tls_transport.h>
#include <web_socket.h>
#include <esp_log.h>

#include <wifi_station.h>
#include <wifi_configuration_ap.h>
#include <ssid_manager.h>
#include "system_info.h"

#define BOARD_NAME "ESP32-S3-DevKitC-1"
#define BOARD_TYPE "ESP32-S3-DevKitC-1"
static const char *TAG = "WifiBoard";
#include <device_status_manager.h>
WifiBoard::WifiBoard() {
    Settings settings("wifi", true);
    wifi_config_mode_ = settings.GetInt("force_ap") == 1;
    if (wifi_config_mode_) {
        ESP_LOGI(TAG, "force_ap is set to 1, reset to 0");
        settings.SetInt("force_ap", 0);
    }
}

std::string WifiBoard::GetIpAddress() 
{
    auto& wifi_station = WifiStation::GetInstance();
    return wifi_station.GetIpAddress();
}
std::string WifiBoard::GetBoardType() {
    return "wifi";
}

void WifiBoard::EnterWifiConfigMode() {
    //auto& application = Application::GetInstance();
    //application.SetDeviceState(kDeviceStateWifiConfiguring);

    auto& wifi_ap = WifiConfigurationAp::GetInstance();
    wifi_ap.SetLanguage("zh-CN");
    wifi_ap.SetSsidPrefix("Xiaozhi");
    wifi_ap.Start();

    // 显示 WiFi 配置 AP 的 SSID 和 Web 服务器 URL
    //std::string hint = Lang::Strings::CONNECT_TO_HOTSPOT;
    std::string hint = "手机连接热点";
    hint += wifi_ap.GetSsid();
    hint += "通过浏览器访问";
    hint += wifi_ap.GetWebServerUrl();
    hint += "\n\n";
    
    // 获取原始字符串长度
    size_t len = hint.length() + 1; // +1 for null terminator

    // 分配SPI RAM内存
    char* hint_buffer =(char *) heap_caps_malloc(len, MALLOC_CAP_SPIRAM);

    // 复制内容到SPI RAM
    strcpy(hint_buffer, hint.c_str());

    auto &DeviceStatusManger = DeviceStatusManger::GetInstance();
    DeviceStateMsg_t msg = {STATE_DISCONNECTED, hint_buffer};
    DeviceStatusManger.SetDeviceStatus(&msg);
    
    // Wait forever until reset after configuration
    while (true) {
        int free_sram = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
        int min_free_sram = heap_caps_get_minimum_free_size(MALLOC_CAP_INTERNAL);
        ESP_LOGI(TAG, "Free internal: %u minimal internal: %u", free_sram, min_free_sram);
        vTaskDelay(pdMS_TO_TICKS(10000));
    }
}


void WifiBoard::StartNetwork() {
    // User can press BOOT button while starting to enter WiFi configuration mode
    if (wifi_config_mode_) {
        EnterWifiConfigMode();
        return;
    }

    // If no WiFi SSID is configured, enter WiFi configuration mode
    auto& ssid_manager = SsidManager::GetInstance();
    auto ssid_list = ssid_manager.GetSsidList();
    if (ssid_list.empty()) {
        wifi_config_mode_ = true;
        EnterWifiConfigMode();
        return;
    }

    auto& wifi_station = WifiStation::GetInstance();
    wifi_station.OnScanBegin([this]() {
        ESP_LOGI(TAG, "Scanning WiFi...");
        auto & dev_stat_manager = DeviceStatusManger::GetInstance();
        DeviceStateMsg_t msg = {STATE_NETWORK_SCANNING, nullptr};
        dev_stat_manager.SetDeviceStatus(&msg);
        
    });
    wifi_station.OnConnect([this](const std::string& ssid) {
        auto & dev_stat_manager = DeviceStatusManger::GetInstance();
        DeviceStateMsg_t msg = {STATE_NETWORK_CONFIG, nullptr};
        dev_stat_manager.SetDeviceStatus(&msg);
    });
    wifi_station.OnConnected([this](const std::string& ssid) {
        auto & dev_stat_manager = DeviceStatusManger::GetInstance();
        DeviceStateMsg_t msg = {STATE_NETWORK_SUCCESS, nullptr};
        dev_stat_manager.SetDeviceStatus(&msg);
    });
    wifi_station.Start();

    // Try to connect to WiFi, if failed, launch the WiFi configuration AP
    if (!wifi_station.WaitForConnected(60 * 1000)) {
        wifi_station.Stop();
        wifi_config_mode_ = true;
        EnterWifiConfigMode();
        return;
    }
}

Http* WifiBoard::CreateHttp() {
    return new EspHttp();
}

WebSocket* WifiBoard::CreateWebSocket() {

    std::string url = CONFIG_MOKEAI_WS_URL;
    if (url.find("wss://") == 0) {
        return new WebSocket(new TlsTransport());
    } else {
        return new WebSocket(new TcpTransport());
    }

    return nullptr;
}
void WifiBoard::SetNetWorkKeepAlive(int keep_alive_seconds)
{
    
}Mqtt* WifiBoard::CreateMqtt() {
    return new EspMqtt();
}

Udp* WifiBoard::CreateUdp() {
    return new EspUdp();
}

const char* WifiBoard::GetNetworkStateIcon() {
    if (wifi_config_mode_) {
        // todo: return FONT_AWESOME_WIFI_OFF;
        //return FONT_AWESOME_WIFI;
    }
    auto& wifi_station = WifiStation::GetInstance();
    if (!wifi_station.IsConnected()) {
        //todo : return FONT_AWESOME_WIFI_OFF;
        //return FONT_AWESOME_WIFI_OFF;
    }
    int8_t rssi = wifi_station.GetRssi();
    if (rssi >= -60) {
        //todo: return FONT_AWESOME_WIFI;
        //return FONT_AWESOME_WIFI;
    } else if (rssi >= -70) {
        // todo: return FONT_AWESOME_WIFI_GOOD;
       // return FONT_AWESOME_WIFI_FAIR;
    } else {
        // todo: return FONT_AWESOME_WIFI_WEAK;
        //return FONT_AWESOME_WIFI_WEAK;
    }
    return "";
}

std::string WifiBoard::GetBoardJson() {
    // Set the board type for OTA
    auto& wifi_station = WifiStation::GetInstance();
    std::string board_json = std::string("{\"type\":\"" BOARD_TYPE "\",");
    board_json += "\"name\":\"" BOARD_NAME "\",";
    if (!wifi_config_mode_) {
        board_json += "\"ssid\":\"" + wifi_station.GetSsid() + "\",";
        board_json += "\"rssi\":" + std::to_string(wifi_station.GetRssi()) + ",";
        board_json += "\"channel\":" + std::to_string(wifi_station.GetChannel()) + ",";
        board_json += "\"ip\":\"" + wifi_station.GetIpAddress() + "\",";
    }
    board_json += "\"mac\":\"" + SystemInfo::GetMacAddress() + "\"}";
    return board_json;
}

void WifiBoard::SetPowerSaveMode(bool enabled) {
    auto& wifi_station = WifiStation::GetInstance();
    wifi_station.SetPowerSaveMode(enabled);
}

void WifiBoard::ResetWifiConfiguration() {
    // Set a flag and reboot the device to enter the network configuration mode
    {
        Settings settings("wifi", true);
        settings.SetInt("force_ap", 1);
    }
    //GetDisplay()->ShowNotification(Lang::Strings::ENTERING_WIFI_CONFIG_MODE);
    vTaskDelay(pdMS_TO_TICKS(1000));
    // Reboot the device
    esp_restart();
}
