#ifndef STEPPER_H
#define STEPPER_H

#include <cstdint>

class Stepper {
    public:
        virtual ~Stepper() = default;
        

        virtual void  set_direction(bool direction) = 0;
        virtual void enable() = 0;
        virtual void disable() = 0;
        virtual void step(uint32_t steps) = 0;
};


class NoStepper : public Stepper {
    public:
        virtual ~NoStepper() = default;
        virtual void  set_direction(bool direction) override {};
        virtual void enable() override {};
        virtual void disable() override {};
        virtual void step(uint32_t steps) override {};
};
#endif