#ifndef BOARD_H
#define BOARD_H

#include <http.h>
#include <web_socket.h>
#include <mqtt.h>
#include <udp.h>
#include <string>
#include "audio_hal.h"
#include "led/led.h"
#include "backlight.h"
#include "esp_log.h"
#include "stepper.h"


void* create_board();
class AudioCodec;
class Display;
class Board {
private:
    Board(const Board&) = delete; // 禁用拷贝构造函数
    Board& operator=(const Board&) = delete; // 禁用赋值操作
    virtual std::string GetBoardJson() = 0;

protected:
    Board();
    std::string GenerateUuid();

    // 软件生成的设备唯一标识
    std::string uuid_;

public:
    static Board& GetInstance() {
        static Board* instance = static_cast<Board*>(create_board());
        return *instance;
    }

    virtual ~Board() = default;
    virtual std::string GetBoardType() = 0;
    virtual std::string GetBoardName() = 0;
    virtual std::string GetUuid() { return uuid_; }
    virtual void SetDisEmoji(void *lv_obj, const char* type) = 0;
    virtual void SetDisText(void *lv_obj,const char *text) = 0;
    virtual Backlight* GetBacklight() { return nullptr; }
    virtual Led* GetLed();
    virtual Stepper *GetStpper(uint32_t stpper_idx);
    virtual Http* CreateHttp() = 0;
    virtual WebSocket* CreateWebSocket() = 0;
    virtual Mqtt* CreateMqtt() = 0;
    virtual Udp* CreateUdp() = 0;
    virtual void SetNetWorkKeepAlive(int keep_alive_seconds) = 0;
    virtual void StartNetwork() = 0;
    virtual const char* GetNetworkStateIcon() = 0;
    virtual bool GetBatteryLevel(int &level, bool& charging);
    virtual std::string GetJson();
    virtual void SetPowerSaveMode(bool enabled) = 0;
    virtual std::string GetIpAddress() = 0;
    virtual void Setvolume(int vol) = 0;
    virtual void Getvolume(int *vol) = 0;
    virtual bool GetLocation(double& longitude, double& latitude);
};

#define DECLARE_BOARD(BOARD_CLASS_NAME) \
void* create_board() { \
    return new BOARD_CLASS_NAME(); \
}

#endif // BOARD_H
