#ifndef WIFI_BOARD_H
#define WIFI_BOARD_H

#include "base_board.h"

class WifiBoard : public Board {
protected:
    bool wifi_config_mode_ = false;

    WifiBoard();
    void EnterWifiConfigMode();
    virtual std::string GetBoardJson() override;

public:
    std::string GetIpAddress() override;
    virtual std::string GetBoardType() override;
    virtual void SetNetWorkKeepAlive(int keep_alive_seconds) override;
    virtual void StartNetwork() override;
    virtual Http* CreateHttp() override;
    virtual WebSocket* CreateWebSocket() override;
    virtual Mqtt* CreateMqtt() override;
    virtual Udp* CreateUdp() override;
    virtual std::string GetBoardName() override {return "";};
    virtual const char* GetNetworkStateIcon() override;
    virtual void SetPowerSaveMode(bool enabled) override;
    virtual void ResetWifiConfiguration();
    virtual void SetDisEmoji(void *lv_obj, const char* type) override {};
    virtual void SetDisText(void *lv_obj,const char *text) override {};

};

#endif // WIFI_BOARD_H
