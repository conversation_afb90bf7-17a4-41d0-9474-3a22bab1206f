/*This is tone file*/
#include <esp_log.h>
#include "stdio.h"
#include "audio_tone_uri.h"

static  char  *tone_uri[] = 
{
    "spiffs://spiffs/dingding.mp3",
    "spiffs://spiffs/ota_successed.mp3",
    "spiffs://spiffs/ota_start.mp3",
    "spiffs://spiffs/welcome.mp3",
    "spiffs://spiffs/button.mp3",
    "spiffs://spiffs/charged.mp3",
    "spiffs://spiffs/net_linked.mp3",
    "spiffs://spiffs/blue_linked.mp3",
    "spiffs://spiffs/touch_welcome.mp3",
    "spiffs://spiffs/dev_auth_succ.mp3",
    "spiffs://spiffs/dev_auth_failed.mp3",
    "spiffs://spiffs/con_service_timeout.mp3",
};

const char* get_tone_url(tone_music_t tone_type) 
{
    if (tone_type < 0 || tone_type >= TONE_MAX) {
        ESP_LOGE("TONE", "Invalid tone type: %d", tone_type);
        return NULL;
    }
    return tone_uri[tone_type];
}
