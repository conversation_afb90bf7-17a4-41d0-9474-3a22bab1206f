#ifndef PROTOCOL_H
#define PROTOCOL_H

#include <cJSON.h>
#include <string>
#include <functional>
#include <chrono>

struct BinaryProtocol3 {
    uint8_t type;
    uint8_t reserved;
    uint16_t payload_size;
    uint8_t payload[];
} __attribute__((packed));

enum AbortReason {
    kAbortReasonNone,
    kAbortReasonWakeWordDetected
};

enum ListeningMode {
    kListeningModeAutoStop,
    kListeningModeManualStop,
    kListeningModeAlwaysOn // 需要 AEC 支持
};


enum connection_state {
    kConnectionStateConnecting,
    kConnectionStateConnected,
    kConnectionStateDisconnected
};
enum authentication_state {
    kAuthenticationStateNotAuthenticated,
    kAuthenticationStateAuthenticated
};
class Protocol {
public:
    virtual ~Protocol() = default;

    inline int server_sample_rate() const {
        return server_sample_rate_;
    }
    inline const std::string& session_id() const {
        return session_id_;
    }

    void OnIncomingAudio(std::function<void(std::vector<uint8_t>&& data)> callback);
    void OnIncomingJson(std::function<void(const cJSON* root)> callback);
    void OnAudioChannelOpened(std::function<void()> callback);
    void OnAudioChannelClosed(std::function<void()> callback);
    void OnNetworkError(std::function<void(const std::string& message)> callback);

    virtual void Start() = 0;
    virtual bool OpenAudioChannel() = 0;
    virtual void CloseAudioChannel() = 0;
    virtual uint32_t GetServerRetCode() const = 0;
    virtual std::string GetServerMsgType() const = 0;
    virtual enum connection_state GetConnectionState() const = 0;
    virtual enum authentication_state GetAuthenticationState() const = 0;
    virtual void SetServerRetCode(uint32_t code)  = 0;
    virtual bool DeviceAuthenticate(void) = 0;
    virtual bool IsAudioChannelOpened() const = 0;
    virtual void SendAudio(const std::vector<uint8_t>& data) = 0;
    virtual void SendWakeWordDetected(const std::string& wake_word);
    virtual void SendStartListening(ListeningMode mode);
    virtual void SendTouchDetect(void) = 0;
    virtual void SendStopListening();
    virtual void SendAutoExit() = 0; 
    virtual void SendManualExit() = 0;
    virtual void SendAbortSpeaking(AbortReason reason);
    virtual void SendIotDescriptors(const std::string& descriptors);
    virtual void SendIotStates(const std::string& states);

protected:
    std::function<void(const cJSON* root)> on_incoming_json_;
    std::function<void(std::vector<uint8_t>&& data)> on_incoming_audio_;
    std::function<void()> on_audio_channel_opened_;
    std::function<void()> on_audio_channel_closed_;
    std::function<void(const std::string& message)> on_network_error_;

    int server_sample_rate_ = 16000;
    bool error_occurred_ = false;
    std::string session_id_;
    enum connection_state connection_state_ = kConnectionStateDisconnected;
    enum authentication_state authentication_state_ = kAuthenticationStateNotAuthenticated;
    std::chrono::time_point<std::chrono::steady_clock> last_incoming_time_;

    virtual void SendText(const std::string& text) = 0;
    virtual void SetError(const std::string& message);
    virtual bool IsTimeout() const;
};

#endif // PROTOCOL_H

