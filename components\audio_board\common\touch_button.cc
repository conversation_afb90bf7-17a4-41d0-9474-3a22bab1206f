#include "touch_button.h"
#include "esp_log.h"
#include "device_status_manager.h"

static const char* TAG = "TouchSensor";

typedef struct touch_msg {
    touch_pad_intr_mask_t intr_mask;
    uint32_t pad_num;
    uint32_t pad_status;
    uint32_t pad_val;
} touch_event_t;

TouchSensor::TouchSensor(uint8_t pad_num, float threshold) : 
    padnum_(pad_num), threshold_(threshold) {
    uint32_t touch_value;
    
    if (que_touch == NULL) {
        que_touch = xQueueCreate(10, sizeof(touch_event_t));
    }

    touch_pad_init();
    //touch_pad_set_voltage(TOUCH_HVOLT_2V7, TOUCH_LVOLT_0V5, TOUCH_HVOLT_ATTEN_1V);

    touch_pad_config((touch_pad_t)pad_num);
    
        /* Denoise setting at TouchSensor 0. */
        touch_pad_denoise_t denoise = {
            /* The bits to be cancelled are determined according to the noise level. */
            .grade = TOUCH_PAD_DENOISE_BIT4,
            /* By adjusting the parameters, the reading of T0 should be approximated to the reading of the measured channel. */
            .cap_level = TOUCH_PAD_DENOISE_CAP_L4,
        };
        touch_pad_denoise_set_config(&denoise);
        touch_pad_denoise_enable();
        ESP_LOGI(TAG, "Denoise function init");
#if 0
     /* Waterproof function */
     touch_pad_waterproof_t waterproof = {
        .guard_ring_pad = button[3],   // If no ring pad, set 0;
        /* It depends on the number of the parasitic capacitance of the shield pad.
           Based on the touch readings of T14 and T0, estimate the size of the parasitic capacitance on T14
           and set the parameters of the appropriate hardware. */
        .shield_driver = TOUCH_PAD_SHIELD_DRV_L2,
    };
    touch_pad_waterproof_set_config(&waterproof);
    touch_pad_waterproof_enable();
    ESP_LOGI(TAG, "touch pad waterproof init");
#endif

    /* Filter setting */

    touch_filter_config_t filter_info = {
        .mode = TOUCH_PAD_FILTER_IIR_4,           // Test jitter and filter 1/4.
        .debounce_cnt = 1,      // 1 time count.
        .noise_thr = 0,         // 50%
        .jitter_step = 0,       // use for jitter mode.
        .smh_lvl = TOUCH_PAD_SMOOTH_IIR_2,
    };
    touch_pad_filter_set_config(&filter_info);
    touch_pad_filter_enable();
    ESP_LOGI(TAG, "touch pad filter init");

    touch_pad_timeout_set(true, TOUCH_PAD_THRESHOLD_MAX);
    /* Register touch interrupt ISR, enable intr type. */
    touch_pad_isr_register(interruptHandler, this, (touch_pad_intr_mask_t)TOUCH_PAD_INTR_MASK_ALL);
    touch_pad_intr_enable(touch_pad_intr_mask_t(TOUCH_PAD_INTR_MASK_ACTIVE | TOUCH_PAD_INTR_MASK_INACTIVE | TOUCH_PAD_INTR_MASK_TIMEOUT));

    /* Enable touch sensor clock. Work mode is "timer trigger". */
    touch_pad_set_fsm_mode(TOUCH_FSM_MODE_TIMER);
    touch_pad_fsm_start();

    // 必须要等touchsensor 初始化好了，在去设置触发阈值，不然的话
    // 会有设置阈值太大的情况，导致touch sensor始终无法触发
    
    vTaskDelay(100/ portTICK_PERIOD_MS);

    touch_pad_read_benchmark((touch_pad_t)pad_num, &touch_value);
    ESP_LOGI(TAG, "[%d] touch benchmark value: %d", (int)pad_num, (int)touch_value);
    touch_pad_set_thresh((touch_pad_t)pad_num, touch_value * threshold);

    xTaskCreate(&touch_state_recive, "touch_pad_read_task", 4096, this, 5, NULL);

    ESP_LOGI(TAG, "Touch sensor initialized");

    

}

TouchSensor::~TouchSensor() {
   
}

void TouchSensor::touch_state_recive(void *arg)
{
    touch_event_t evt;
    static uint8_t guard_mode_flag = 0;
    TouchSensor* self = static_cast<TouchSensor*>(arg);
    while (1) 
    {
        int ret = xQueueReceive(self->que_touch, &evt, (TickType_t)portMAX_DELAY);
        if (ret != pdTRUE) {
            continue;
        }
        if (evt.intr_mask & TOUCH_PAD_INTR_MASK_ACTIVE) {
            /* if guard pad be touched, other pads no response. */
            if (evt.pad_num == 0) {
                guard_mode_flag = 1;
                ESP_LOGW(TAG, "TouchSensor [%"PRIu32"] be activated, enter guard mode", evt.pad_num);
            } else {
                if (guard_mode_flag == 0) {
                    
                    if (self->on_touch_cb_ != nullptr)
                    {
                        self->on_touch_cb_();
                    }
                    ESP_LOGI(TAG, "TouchSensor [%"PRIu32"] be activated, status mask 0x%"PRIu32"", evt.pad_num, evt.pad_status);
                } else {
                    ESP_LOGW(TAG, "In guard mode. No response");
                }
            }
        }
        if (evt.intr_mask & TOUCH_PAD_INTR_MASK_INACTIVE) {
            /* if guard pad be touched, other pads no response. */
            if (evt.pad_num == 0) {
                guard_mode_flag = 0;
                ESP_LOGW(TAG, "TouchSensor [%" PRIu32 "] be inactivated, exit guard mode", evt.pad_num);
            } else {
                if (guard_mode_flag == 0) {
                    ESP_LOGI(TAG, "TouchSensor [%" PRIu32 "] be inactivated, status mask 0x%" PRIu32, evt.pad_num, evt.pad_status);
                }
            }
        }
        if (evt.intr_mask & TOUCH_PAD_INTR_MASK_SCAN_DONE) {
            ESP_LOGI(TAG, "The touch sensor group measurement is done [%" PRIu32 "].", evt.pad_num);
        }
        if (evt.intr_mask & TOUCH_PAD_INTR_MASK_TIMEOUT) {
            /* Add your exception handling in here. */
            ESP_LOGI(TAG, "Touch sensor channel %" PRIu32 " measure timeout. Skip this exception channel!!", evt.pad_num);
            touch_pad_timeout_resume(); // Point on the next channel to measure.
        }
    }
}
void TouchSensor::interruptHandler(void* arg) 
{
    int task_awoken = pdFALSE;
    touch_event_t evt;
    TouchSensor* self = static_cast<TouchSensor*>(arg);

    evt.intr_mask = (touch_pad_intr_mask_t)touch_pad_read_intr_status_mask();
    evt.pad_status = touch_pad_get_status();
    evt.pad_num = touch_pad_get_current_meas_channel();

    xQueueSendFromISR(self->que_touch, &evt, &task_awoken);
    if (task_awoken == pdTRUE) {
        portYIELD_FROM_ISR();
    }
}
